"use client"

import type React from "react"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { ArrowLef<PERSON>, Save, RefreshCw } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { useToast } from "@/hooks/use-toast"

export default function NewPaperPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    title: "",
    authors: "",
    venue: "",
    year: "",
    doi: "",
    url: "",
    tags: "",
  })
  const [isEnriching, setIsEnriching] = useState(false)
  const [isDoiMode, setIsDoiMode] = useState(true)
  const [enrichedData, setEnrichedData] = useState<any>(null)

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Validate required fields
    if (isDoiMode && !enrichedData) {
      toast({ title: "Please fetch paper details first", variant: "destructive" })
      return
    }

    if (!isDoiMode && !formData.title.trim()) {
      toast({ title: "Title is required", variant: "destructive" })
      return
    }

    try {
      const paperData = {
        ...formData,
        authors: formData.authors
          .split(",")
          .map((a) => a.trim())
          .filter(Boolean),
        tags: formData.tags
          .split(",")
          .map((t) => t.trim())
          .filter(Boolean),
        year: formData.year ? Number.parseInt(formData.year) : undefined,
        // Include enriched data if available
        ...(enrichedData && {
          abstract: enrichedData.abstract,
          citationCount: enrichedData.citationCount,
          referenceCount: enrichedData.referenceCount,
          publicationDate: enrichedData.publicationDate,
          journal: enrichedData.journal,
          volume: enrichedData.volume,
          issue: enrichedData.issue,
          pages: enrichedData.pages,
        })
      }

      const response = await fetch("/api/papers", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(paperData),
      })

      if (response.ok) {
        const paper = await response.json()
        const hasEnrichedData = !!enrichedData
        toast({
          title: "Paper created successfully",
          description: hasEnrichedData ? "Enriched with Semantic Scholar data" : undefined
        })
        router.push(`/papers/${paper.id}`)
      } else {
        throw new Error("Failed to create paper")
      }
    } catch (error) {
      toast({ title: "Error creating paper", variant: "destructive" })
    }
  }

  const fetchFromDOI = async () => {
    if (!formData.doi) return

    try {
      const response = await fetch(`/api/papers/doi/${encodeURIComponent(formData.doi)}`)
      if (response.ok) {
        const data = await response.json()
        setFormData((prev) => ({
          ...prev,
          title: data.title || prev.title,
          authors: data.authors?.join(", ") || prev.authors,
        }))
        toast({ title: "Paper details fetched from DOI" })
      }
    } catch (error) {
      toast({ title: "Failed to fetch from DOI", variant: "destructive" })
    }
  }

  const enrichFromDOI = async () => {
    if (!formData.doi.trim()) {
      toast({ title: "DOI is required for enrichment", variant: "destructive" })
      return
    }

    setIsEnriching(true)
    try {
      const response = await fetch("/api/papers/enrich", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          doi: formData.doi.trim()
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.enriched) {
          // Store enriched data and update form
          setEnrichedData(data.metadata)
          setFormData(prev => ({
            ...prev,
            title: data.metadata.title || prev.title,
            authors: data.metadata.authors?.join(", ") || prev.authors,
            venue: data.metadata.venue || prev.venue,
            year: data.metadata.year?.toString() || prev.year,
            url: data.metadata.url || prev.url,
          }))

          toast({
            title: "Paper found and enriched!",
            description: `Found "${data.metadata.title}" with ${data.metadata.citationCount || 0} citations`
          })

          // Switch to manual mode to allow editing
          setIsDoiMode(false)
        } else {
          toast({
            title: "Paper not found",
            description: "DOI not found in Semantic Scholar database. You can enter details manually."
          })
          setIsDoiMode(false)
        }
      } else {
        throw new Error("Failed to enrich paper")
      }
    } catch (error) {
      console.error("Failed to enrich paper:", error)
      toast({ title: "Failed to enrich paper", variant: "destructive" })
      setIsDoiMode(false)
    } finally {
      setIsEnriching(false)
    }
  }

  const enrichPaper = async () => {
    if (!formData.title.trim()) {
      toast({ title: "Title is required for enrichment", variant: "destructive" })
      return
    }

    setIsEnriching(true)
    try {
      const response = await fetch("/api/papers/enrich", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: formData.title.trim(),
          authors: formData.authors.split(",").map((author) => author.trim()).filter(Boolean),
          doi: formData.doi.trim() || undefined
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.enriched) {
          // Update form with enriched metadata
          setEnrichedData(data.metadata)
          setFormData(prev => ({
            ...prev,
            authors: data.metadata.authors?.join(", ") || prev.authors,
            venue: data.metadata.venue || prev.venue,
            year: data.metadata.year?.toString() || prev.year,
            doi: data.metadata.doi || prev.doi,
            url: data.metadata.url || prev.url,
          }))

          toast({
            title: "Paper enriched successfully",
            description: `Found ${data.metadata.citationCount || 0} citations, ${data.metadata.referenceCount || 0} references`
          })
        } else {
          toast({
            title: "No additional data found",
            description: "Paper not found in Semantic Scholar database"
          })
        }
      } else {
        throw new Error("Failed to enrich paper")
      }
    } catch (error) {
      console.error("Failed to enrich paper:", error)
      toast({ title: "Failed to enrich paper", variant: "destructive" })
    } finally {
      setIsEnriching(false)
    }
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <Link href="/papers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h1 className="text-2xl font-bold">New Paper</h1>
        </div>
      </header>

      <div className="flex-1 overflow-auto p-6">
        <form onSubmit={handleSubmit} className="max-w-2xl mx-auto space-y-6">
          {isDoiMode ? (
            // DOI-first mode
            <div className="space-y-6">
              <div className="text-center space-y-4">
                <h2 className="text-xl font-semibold">Add Paper from DOI</h2>
                <p className="text-muted-foreground">
                  Enter a DOI to automatically fetch paper details from Semantic Scholar
                </p>
              </div>

              <div className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="doi">DOI</Label>
                  <Input
                    id="doi"
                    value={formData.doi}
                    onChange={(e) => setFormData((prev) => ({ ...prev, doi: e.target.value }))}
                    placeholder="10.1000/182"
                    className="text-lg"
                  />
                </div>

                <div className="flex gap-2">
                  <Button
                    type="button"
                    onClick={enrichFromDOI}
                    disabled={!formData.doi.trim() || isEnriching}
                    className="flex-1"
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${isEnriching ? 'animate-spin' : ''}`} />
                    {isEnriching ? 'Fetching...' : 'Fetch Paper Details'}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setIsDoiMode(false)}
                  >
                    Enter Manually
                  </Button>
                </div>
              </div>
            </div>
          ) : (
            // Manual entry mode
            <>
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Paper Details</h2>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => setIsDoiMode(true)}
                >
                  Back to DOI Entry
                </Button>
              </div>

              <div className="space-y-2">
                <Label htmlFor="doi">DOI (optional)</Label>
                <Input
                  id="doi"
                  value={formData.doi}
                  onChange={(e) => setFormData((prev) => ({ ...prev, doi: e.target.value }))}
                  placeholder="10.1000/182"
                />
              </div>

          <div className="space-y-2">
            <Label htmlFor="title">Title *</Label>
            <Textarea
              id="title"
              value={formData.title}
              onChange={(e) => setFormData((prev) => ({ ...prev, title: e.target.value }))}
              placeholder="Enter paper title..."
              required
              rows={3}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="authors">Authors *</Label>
            <Textarea
              id="authors"
              value={formData.authors}
              onChange={(e) => setFormData((prev) => ({ ...prev, authors: e.target.value }))}
              placeholder="Author 1, Author 2, Author 3..."
              required
              rows={2}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="venue">Venue</Label>
              <Input
                id="venue"
                value={formData.venue}
                onChange={(e) => setFormData((prev) => ({ ...prev, venue: e.target.value }))}
                placeholder="Conference/Journal"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="year">Year</Label>
              <Input
                id="year"
                type="number"
                value={formData.year}
                onChange={(e) => setFormData((prev) => ({ ...prev, year: e.target.value }))}
                placeholder="2024"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="url">URL</Label>
            <Input
              id="url"
              type="url"
              value={formData.url}
              onChange={(e) => setFormData((prev) => ({ ...prev, url: e.target.value }))}
              placeholder="https://..."
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="tags">Tags</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData((prev) => ({ ...prev, tags: e.target.value }))}
              placeholder="machine learning, nlp, transformers..."
            />
          </div>

              <div className="flex gap-2">
                <Button
                  type="button"
                  variant="outline"
                  onClick={enrichPaper}
                  disabled={isEnriching || !formData.title.trim()}
                  className="flex-1"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${isEnriching ? 'animate-spin' : ''}`} />
                  Enrich from Semantic Scholar
                </Button>
                <Button type="submit" className="flex-1" disabled={!formData.title.trim()}>
                  <Save className="h-4 w-4 mr-2" />
                  Create Paper
                </Button>
              </div>
            </>
          )}

          {/* Create button for DOI mode */}
          {isDoiMode && enrichedData && (
            <Button type="submit" className="w-full" size="lg">
              <Save className="h-4 w-4 mr-2" />
              Create Paper: {enrichedData.title}
            </Button>
          )}
        </form>
      </div>
    </div>
  )
}
