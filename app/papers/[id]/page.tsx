"use client"

import { useState, useEffect, useCallback } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { ArrowLeft, Star, Trash2, BookOpen, BookOpenCheck, ExternalLink, FileText, BarChart3, RefreshC<PERSON> } from "lucide-react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { useToast } from "@/hooks/use-toast"
import { CollectionToggle } from "@/components/collection-toggle"
import type { Paper, Note } from "@/lib/types"

export default function PaperPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [paper, setPaper] = useState<Paper | null>(null)
  const [note, setNote] = useState<Note | null>(null)
  const [loading, setLoading] = useState(true)
  const [reviewStatus, setReviewStatus] = useState<{
    inReview: boolean
    isDue: boolean
    daysUntilDue: number | null
  } | null>(null)
  const [isEnriching, setIsEnriching] = useState(false)

  const paperId = params.id as string

  useEffect(() => {
    fetchPaper()
    fetchNote()
    fetchReviewStatus()
  }, [paperId])

  const fetchPaper = async () => {
    try {
      const response = await fetch(`/api/papers/${paperId}`)
      if (response.ok) {
        const data = await response.json()
        setPaper(data)
      }
    } catch (error) {
      console.error("Failed to fetch paper:", error)
    } finally {
      setLoading(false)
    }
  }

  const fetchNote = async () => {
    try {
      const response = await fetch(`/api/notes/${paperId}`)
      if (response.ok) {
        const data = await response.json()
        setNote(data)
      } else {
        // Create empty note if none exists
        setNote({
          id: "",
          paperId,
          bullets: ["", "", "", "", "", ""],
          whyItMatters: "",
          figureRefs: [],
        })
      }
    } catch (error) {
      console.error("Failed to fetch note:", error)
    }
  }

  const fetchReviewStatus = async () => {
    try {
      const response = await fetch(`/api/papers/${paperId}/review`)
      if (response.ok) {
        const data = await response.json()
        setReviewStatus(data)
      }
    } catch (error) {
      console.error("Failed to fetch review status:", error)
    }
  }

  const addToReview = async () => {
    try {
      const response = await fetch(`/api/papers/${paperId}/review`, {
        method: "POST",
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Added to review queue",
          description: data.wasExisting ? "Paper review updated" : "Paper added to review queue"
        })
        fetchReviewStatus() // Refresh status
      } else {
        throw new Error("Failed to add to review")
      }
    } catch (error) {
      console.error("Failed to add to review:", error)
      toast({ title: "Failed to add to review", variant: "destructive" })
    }
  }

  const removeFromReview = async () => {
    try {
      const response = await fetch(`/api/papers/${paperId}/review`, {
        method: "DELETE",
      })

      if (response.ok) {
        toast({ title: "Removed from review queue" })
        fetchReviewStatus() // Refresh status
      } else {
        throw new Error("Failed to remove from review")
      }
    } catch (error) {
      console.error("Failed to remove from review:", error)
      toast({ title: "Failed to remove from review", variant: "destructive" })
    }
  }

  const enrichPaper = async () => {
    if (!paper) return

    setIsEnriching(true)
    try {
      const response = await fetch("/api/papers/enrich", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          title: paper.title,
          authors: paper.authors,
          doi: paper.doi
        })
      })

      if (response.ok) {
        const data = await response.json()
        if (data.enriched) {
          // Update paper with enriched metadata
          const enrichedPaper = { ...paper, ...data.metadata }
          setPaper(enrichedPaper)

          // Save the enriched data
          await savePaper(data.metadata)

          toast({
            title: "Paper enriched successfully",
            description: `Added ${data.metadata.citationCount || 0} citations, ${data.metadata.referenceCount || 0} references`
          })
        } else {
          toast({
            title: "No additional data found",
            description: "Paper not found in Semantic Scholar database"
          })
        }
      } else {
        throw new Error("Failed to enrich paper")
      }
    } catch (error) {
      console.error("Failed to enrich paper:", error)
      toast({ title: "Failed to enrich paper", variant: "destructive" })
    } finally {
      setIsEnriching(false)
    }
  }

  const savePaper = useCallback(
    async (updatedPaper: Partial<Paper>) => {
      if (!paper) return

      try {
        const response = await fetch(`/api/papers/${paperId}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(updatedPaper),
        })

        if (response.ok) {
          const updated = await response.json()
          setPaper(updated)
        }
      } catch (error) {
        console.error("Failed to save paper:", error)
      }
    },
    [paper, paperId],
  )

  const saveNote = useCallback(
    async (updatedNote: Partial<Note>) => {
      if (!note) return

      try {
        const response = await fetch(`/api/notes/${paperId}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify(updatedNote),
        })

        if (response.ok) {
          const updated = await response.json()
          setNote(updated)
        }
      } catch (error) {
        console.error("Failed to save note:", error)
      }
    },
    [note, paperId],
  )

  const toggleStar = async () => {
    if (!paper) return

    try {
      await fetch(`/api/papers/${paperId}/star`, { method: "POST" })
      setPaper((prev) => (prev ? { ...prev, starred: !prev.starred } : null))
    } catch (error) {
      console.error("Failed to toggle star:", error)
    }
  }

  const deletePaper = async () => {
    if (!paper) return

    if (!confirm(`Are you sure you want to delete "${paper.title}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/papers/${paperId}`, { method: "DELETE" })
      if (response.ok) {
        toast({ title: "Paper deleted successfully" })
        router.push("/papers")
      } else {
        throw new Error("Failed to delete paper")
      }
    } catch (error) {
      console.error("Failed to delete paper:", error)
      toast({ title: "Failed to delete paper", variant: "destructive" })
    }
  }

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle shortcuts when typing in inputs or textareas
      const target = e.target as HTMLElement
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
        return
      }

      // Handle Ctrl/Cmd + S for save (though auto-save is already implemented)
      if ((e.metaKey || e.ctrlKey) && e.key === "s") {
        e.preventDefault()
        // Auto-save is handled by debounced updates
        return
      }

      // Handle keyboard shortcuts
      switch (e.key.toLowerCase()) {
        case "s":
          e.preventDefault()
          toggleStar()
          break
        case "e":
          e.preventDefault()
          // Focus on the title field to start editing
          const titleField = document.getElementById("title")
          if (titleField) {
            titleField.focus()
          }
          break
        case "escape":
          e.preventDefault()
          router.push("/papers")
          break
        case "delete":
        case "backspace":
          if (e.shiftKey) {
            e.preventDefault()
            deletePaper()
          }
          break
        case "r":
          e.preventDefault()
          if (reviewStatus?.inReview) {
            removeFromReview()
          } else {
            addToReview()
          }
          break
      }

      // Number keys 1-6 for bullet points
      if (e.key >= "1" && e.key <= "6") {
        const index = Number.parseInt(e.key) - 1
        const bulletInput = document.getElementById(`bullet-${index}`)
        if (bulletInput) {
          e.preventDefault()
          bulletInput.focus()
        }
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [router])

  if (loading) {
    return <div className="p-4">Loading...</div>
  }

  if (!paper) {
    return <div className="p-4">Paper not found</div>
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <Link href="/papers">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          <h1 className="text-xl font-semibold truncate flex-1">{paper.title}</h1>
          <CollectionToggle paperId={paperId} />
          <Button variant="ghost" size="sm" onClick={toggleStar}>
            <Star className={`h-4 w-4 ${paper.starred ? "fill-yellow-400 text-yellow-400" : ""}`} />
          </Button>
          {reviewStatus?.inReview ? (
            <Button
              variant="ghost"
              size="sm"
              onClick={removeFromReview}
              className="text-green-600 hover:text-green-700"
            >
              <BookOpenCheck className="h-4 w-4" />
            </Button>
          ) : (
            <Button variant="ghost" size="sm" onClick={addToReview}>
              <BookOpen className="h-4 w-4" />
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={enrichPaper}
            disabled={isEnriching}
            className="text-blue-600 border-blue-200 hover:bg-blue-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isEnriching ? 'animate-spin' : ''}`} />
            {isEnriching ? 'Enriching...' : 'Enrich'}
          </Button>
          <Button variant="ghost" size="sm" onClick={deletePaper}>
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </header>

      <div className="flex-1 overflow-auto p-6">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Paper Details */}
          <div className="space-y-4">
            {reviewStatus?.inReview && (
              <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
                <BookOpenCheck className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-900 dark:text-blue-100">
                  In Review Queue
                </span>
                {reviewStatus.isDue ? (
                  <span className="text-xs text-blue-700 dark:text-blue-300">• Due now</span>
                ) : reviewStatus.daysUntilDue !== null && (
                  <span className="text-xs text-blue-700 dark:text-blue-300">
                    • Due in {reviewStatus.daysUntilDue} day{reviewStatus.daysUntilDue !== 1 ? 's' : ''}
                  </span>
                )}
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="title">Title</Label>
              <Textarea
                id="title"
                value={paper.title}
                onChange={(e) => {
                  const updated = { ...paper, title: e.target.value }
                  setPaper(updated)
                  savePaper({ title: e.target.value })
                }}
                rows={3}
                className="text-lg font-semibold"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="authors">Authors</Label>
              <Textarea
                id="authors"
                value={paper.authors.join(", ")}
                onChange={(e) => {
                  const authors = e.target.value
                    .split(",")
                    .map((a) => a.trim())
                    .filter(Boolean)
                  const updated = { ...paper, authors }
                  setPaper(updated)
                  savePaper({ authors })
                }}
                rows={2}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="venue">Venue</Label>
                <Input
                  id="venue"
                  value={paper.venue || ""}
                  onChange={(e) => {
                    const updated = { ...paper, venue: e.target.value }
                    setPaper(updated)
                    savePaper({ venue: e.target.value })
                  }}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="year">Year</Label>
                <Input
                  id="year"
                  type="number"
                  value={paper.year || ""}
                  onChange={(e) => {
                    const year = e.target.value ? Number.parseInt(e.target.value) : undefined
                    const updated = { ...paper, year }
                    setPaper(updated)
                    savePaper({ year })
                  }}
                />
              </div>
            </div>

            {/* DOI and URL Section */}
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="doi">DOI</Label>
                <div className="flex gap-2">
                  <Input
                    id="doi"
                    value={paper.doi || ""}
                    placeholder="10.1000/182"
                    onChange={(e) => {
                      const updated = { ...paper, doi: e.target.value }
                      setPaper(updated)
                      savePaper({ doi: e.target.value })
                    }}
                  />
                  {paper.doi && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`https://doi.org/${paper.doi}`, '_blank')}
                      className="shrink-0"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="url">URL</Label>
                <div className="flex gap-2">
                  <Input
                    id="url"
                    value={paper.url || ""}
                    placeholder="https://..."
                    onChange={(e) => {
                      const updated = { ...paper, url: e.target.value }
                      setPaper(updated)
                      savePaper({ url: e.target.value })
                    }}
                  />
                  {paper.url && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(paper.url, '_blank')}
                      className="shrink-0"
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="tags">Tags</Label>
              <Input
                id="tags"
                value={paper.tags.join(", ")}
                onChange={(e) => {
                  const tags = e.target.value
                    .split(",")
                    .map((t) => t.trim())
                    .filter(Boolean)
                  const updated = { ...paper, tags }
                  setPaper(updated)
                  savePaper({ tags })
                }}
              />
            </div>

            {/* Additional Metadata Section */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <BarChart3 className="h-5 w-5" />
                Publication Details
              </h3>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="citationCount">Citation Count</Label>
                  <Input
                    id="citationCount"
                    type="number"
                    value={paper.citationCount || ""}
                    placeholder="0"
                    onChange={(e) => {
                      const citationCount = e.target.value ? Number.parseInt(e.target.value) : undefined
                      const updated = { ...paper, citationCount }
                      setPaper(updated)
                      savePaper({ citationCount })
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="referenceCount">Reference Count</Label>
                  <Input
                    id="referenceCount"
                    type="number"
                    value={paper.referenceCount || ""}
                    placeholder="0"
                    onChange={(e) => {
                      const referenceCount = e.target.value ? Number.parseInt(e.target.value) : undefined
                      const updated = { ...paper, referenceCount }
                      setPaper(updated)
                      savePaper({ referenceCount })
                    }}
                  />
                </div>
              </div>

              <div className="grid grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="journal">Journal</Label>
                  <Input
                    id="journal"
                    value={paper.journal || ""}
                    placeholder="Journal name"
                    onChange={(e) => {
                      const updated = { ...paper, journal: e.target.value }
                      setPaper(updated)
                      savePaper({ journal: e.target.value })
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="volume">Volume</Label>
                  <Input
                    id="volume"
                    value={paper.volume || ""}
                    placeholder="Vol. 1"
                    onChange={(e) => {
                      const updated = { ...paper, volume: e.target.value }
                      setPaper(updated)
                      savePaper({ volume: e.target.value })
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="issue">Issue</Label>
                  <Input
                    id="issue"
                    value={paper.issue || ""}
                    placeholder="Issue 1"
                    onChange={(e) => {
                      const updated = { ...paper, issue: e.target.value }
                      setPaper(updated)
                      savePaper({ issue: e.target.value })
                    }}
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="pages">Pages</Label>
                  <Input
                    id="pages"
                    value={paper.pages || ""}
                    placeholder="1-10"
                    onChange={(e) => {
                      const updated = { ...paper, pages: e.target.value }
                      setPaper(updated)
                      savePaper({ pages: e.target.value })
                    }}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="publicationDate">Publication Date</Label>
                  <Input
                    id="publicationDate"
                    type="date"
                    value={paper.publicationDate || ""}
                    onChange={(e) => {
                      const updated = { ...paper, publicationDate: e.target.value }
                      setPaper(updated)
                      savePaper({ publicationDate: e.target.value })
                    }}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="abstract">Abstract</Label>
                <Textarea
                  id="abstract"
                  value={paper.abstract || ""}
                  placeholder="Paper abstract..."
                  rows={4}
                  onChange={(e) => {
                    const updated = { ...paper, abstract: e.target.value }
                    setPaper(updated)
                    savePaper({ abstract: e.target.value })
                  }}
                />
              </div>
            </div>
          </div>

          {/* Notes Section */}
          {note && (
            <div className="space-y-6">
              <h2 className="text-xl font-semibold">Notes</h2>

              <div className="space-y-4">
                <Label>Key Points (use keys 1-6 to focus)</Label>
                {note.bullets.map((bullet, index) => (
                  <div key={index} className="space-y-2">
                    <Label htmlFor={`bullet-${index}`} className="text-sm text-muted-foreground">
                      Point {index + 1}
                    </Label>
                    <Textarea
                      id={`bullet-${index}`}
                      value={bullet}
                      onChange={(e) => {
                        const bullets = [...note.bullets]
                        bullets[index] = e.target.value
                        const updated = { ...note, bullets }
                        setNote(updated)
                        saveNote({ bullets })
                      }}
                      placeholder={`Key point ${index + 1}...`}
                      rows={2}
                    />
                  </div>
                ))}
              </div>

              <div className="space-y-2">
                <Label htmlFor="whyItMatters">Why It Matters</Label>
                <Textarea
                  id="whyItMatters"
                  value={note.whyItMatters || ""}
                  onChange={(e) => {
                    const updated = { ...note, whyItMatters: e.target.value }
                    setNote(updated)
                    saveNote({ whyItMatters: e.target.value })
                  }}
                  placeholder="Why is this paper important? What are the implications?"
                  rows={4}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="figureRefs">Figure References</Label>
                <Input
                  id="figureRefs"
                  value={note.figureRefs?.join(", ") || ""}
                  onChange={(e) => {
                    const figureRefs = e.target.value
                      .split(",")
                      .map((f) => f.trim())
                      .filter(Boolean)
                    const updated = { ...note, figureRefs }
                    setNote(updated)
                    saveNote({ figureRefs })
                  }}
                  placeholder="Figure 1, Table 2, etc..."
                />
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
