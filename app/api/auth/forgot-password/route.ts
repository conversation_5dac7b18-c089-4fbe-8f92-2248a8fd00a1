import { NextRequest, NextResponse } from 'next/server'
import { users, passwordResetTokens, auditLogs } from '@/lib/database'
import { isValidEmail, generateSecureToken, hashToken, generateTokenExpiry } from '@/lib/auth'
import { getClientIP, getUserAgent } from '@/lib/auth-middleware'
import type { PasswordResetRequest } from '@/lib/types'

export async function POST(request: NextRequest) {
  try {
    const body: PasswordResetRequest = await request.json()
    const { email } = body

    // Validate input
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Find user by email
    const user = await users.getByEmail(email.toLowerCase())

    // Always return success to prevent email enumeration attacks
    // But only send email if user exists
    if (user && user.isActive) {
      // Generate password reset token
      const resetToken = generateSecureToken()
      const tokenHash = hashToken(resetToken)
      const expiresAt = generateTokenExpiry(1) // 1 hour expiry

      // Store token in database
      await passwordResetTokens.create({
        userId: user.id,
        tokenHash,
        expiresAt: expiresAt.toISOString(),
        used: false,
      })

      // Log the password reset request
      const clientIp = getClientIP(request)
      const userAgent = getUserAgent(request)

      await auditLogs.create({
        userId: user.id,
        action: 'password_reset_requested',
        resourceType: 'user',
        resourceId: user.id,
        ipAddress: clientIp,
        userAgent,
        details: { email: user.email },
      })

      // TODO: Send password reset email
      // For now, log the token (remove in production)
      console.log(`Password reset token for ${email}: ${resetToken}`)
      console.log(`Reset URL: http://localhost:3000/auth/reset-password?token=${resetToken}`)
    }

    // Always return success message
    return NextResponse.json({
      message: 'If an account with that email exists, we have sent a password reset link.',
    })

  } catch (error) {
    console.error('Forgot password error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}