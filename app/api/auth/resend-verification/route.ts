import { NextRequest, NextResponse } from 'next/server'
import { users, emailVerificationTokens, auditLogs } from '@/lib/database'
import { isValidEmail, generateSecureToken, generateTokenExpiry } from '@/lib/auth'
import { sendVerificationEmail } from '@/lib/mailer'
import { getClientIP, getUserAgent } from '@/lib/auth-middleware'
import {
  verificationResendLimiter,
  verificationResendDailyLimiter,
  generateRateLimitKey,
  getClientIdentifier,
  logSecurityEvent
} from '@/lib/rate-limiter'
import type { ResendVerificationRequest } from '@/lib/types'

export async function POST(request: NextRequest) {
  try {
    const body: ResendVerificationRequest = await request.json()
    const { email } = body

    // Validate input
    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    const clientIp = getClientIP(request)
    const userAgent = getUserAgent(request)

    // Rate limiting: 1 request per minute per IP+email, 5 per day per email
    const minuteKey = generateRateLimitKey('resend', `${clientIp}:${email}`, 60000)
    const dayKey = generateRateLimitKey('resend-daily', email, 86400000)

    const minuteLimit = verificationResendLimiter.check(minuteKey)
    if (!minuteLimit.allowed) {
      logSecurityEvent('resend_verification_rate_limit_minute', {
        ip: clientIp,
        userAgent,
        email,
        resetTime: new Date(minuteLimit.resetTime).toISOString()
      })

      return NextResponse.json(
        {
          error: 'Too many requests. Please wait a minute before trying again.',
          retryAfter: Math.ceil((minuteLimit.resetTime - Date.now()) / 1000)
        },
        {
          status: 429,
          headers: {
            'Retry-After': Math.ceil((minuteLimit.resetTime - Date.now()) / 1000).toString()
          }
        }
      )
    }

    const dayLimit = verificationResendDailyLimiter.check(dayKey)
    if (!dayLimit.allowed) {
      logSecurityEvent('resend_verification_rate_limit_daily', {
        ip: clientIp,
        userAgent,
        email,
        resetTime: new Date(dayLimit.resetTime).toISOString()
      })

      return NextResponse.json(
        {
          error: 'Daily limit exceeded. Please try again tomorrow.',
          retryAfter: Math.ceil((dayLimit.resetTime - Date.now()) / 1000)
        },
        {
          status: 429,
          headers: {
            'Retry-After': Math.ceil((dayLimit.resetTime - Date.now()) / 1000).toString()
          }
        }
      )
    }

    // Find user by email
    const user = await users.getByEmail(email.toLowerCase())
    
    // Always return success to prevent email enumeration attacks
    // But only send email if user exists and is not verified
    if (user && user.isActive && !user.emailVerified) {
      try {
        // Invalidate any existing verification tokens for this user
        await emailVerificationTokens.invalidateUserTokens(user.id)
        
        // Generate new verification token
        const verificationToken = generateSecureToken()
        const expiresAt = generateTokenExpiry(24) // 24 hours
        
        await emailVerificationTokens.create({
          userId: user.id,
          token: verificationToken,
          expiresAt: expiresAt.toISOString(),
          usedAt: undefined,
        })
        
        // Send verification email
        await sendVerificationEmail(user.email, verificationToken)
        
        // Log the resend request
        await auditLogs.create({
          userId: user.id,
          action: 'verification_email_resent',
          resourceType: 'user',
          resourceId: user.id,
          ipAddress: clientIp,
          userAgent,
          details: { email: user.email },
        })
        
      } catch (emailError) {
        console.error('Failed to resend verification email:', emailError)
        // Continue and return success message to prevent information leakage
      }
    }

    // Always return success message
    return NextResponse.json({
      message: 'If an account with that email exists and is not yet verified, we have sent a new verification link.',
    })

  } catch (error) {
    console.error('Resend verification error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
