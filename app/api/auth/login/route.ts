import { NextRequest, NextResponse } from 'next/server'
import { users, userSessions, auditLogs } from '@/lib/database'
import { verifyPassword, generateToken, generateSessionTokenHash, generateTokenExpiry, sanitizeUser } from '@/lib/auth'
import type { LoginRequest } from '@/lib/types'

export async function POST(request: NextRequest) {
  try {
    const body: LoginRequest = await request.json()
    const { email, password } = body

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Find user by email
    const user = await users.getByEmail(email.toLowerCase())
    if (!user) {
      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // Check if user is active
    if (!user.isActive) {
      return NextResponse.json(
        { error: 'Account is deactivated. Please contact support.' },
        { status: 401 }
      )
    }

    // Verify password
    const isPasswordValid = await verifyPassword(password, user.passwordHash)
    if (!isPasswordValid) {
      // Log failed login attempt
      const clientIp = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
      const userAgent = request.headers.get('user-agent') || 'unknown'

      await auditLogs.create({
        userId: user.id,
        action: 'login_failed',
        resourceType: 'user',
        resourceId: user.id,
        ipAddress: clientIp,
        userAgent,
        details: { reason: 'invalid_password', email: user.email },
      })

      return NextResponse.json(
        { error: 'Invalid email or password' },
        { status: 401 }
      )
    }

    // Check if email is verified
    if (!user.emailVerified) {
      return NextResponse.json(
        {
          error: 'Please verify your email address before signing in. Check your inbox for a verification link.',
          code: 'EMAIL_NOT_VERIFIED',
          email: user.email
        },
        { status: 403 }
      )
    }

    // Generate JWT token
    const token = generateToken(user)
    const tokenHash = generateSessionTokenHash(token)
    const expiresAt = generateTokenExpiry(24) // 24 hours

    // Store session in database
    const clientIp = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'

    await userSessions.create({
      userId: user.id,
      tokenHash,
      expiresAt: expiresAt.toISOString(),
      ipAddress: clientIp,
      userAgent,
    })

    // Update last login time
    await users.update(user.id, {
      lastLogin: new Date().toISOString(),
    })

    // Log successful login
    await auditLogs.create({
      userId: user.id,
      action: 'login_success',
      resourceType: 'user',
      resourceId: user.id,
      ipAddress: clientIp,
      userAgent,
      details: { email: user.email },
    })

    // Create response
    const response = NextResponse.json({
      message: 'Login successful',
      user: sanitizeUser({
        id: user.id,
        email: user.email,
        displayName: user.displayName,
        role: user.role,
        emailVerified: user.emailVerified,
        isActive: user.isActive,
        privacySettings: user.privacySettings,
        preferences: user.preferences,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        lastLogin: user.lastLogin,
      }),
      token,
      expiresAt: expiresAt.toISOString(),
    })

    // Set HTTP-only cookie for additional security
    response.cookies.set('auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 24 * 60 * 60, // 24 hours
      path: '/',
    })

    return response

  } catch (error) {
    console.error('Login error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}