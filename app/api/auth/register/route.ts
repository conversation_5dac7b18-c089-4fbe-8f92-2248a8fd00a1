import { NextRequest, NextResponse } from 'next/server'
import { users, emailVerificationTokens, auditLogs } from '@/lib/database'
import { hashPassword, isValidEmail, isValidPassword, generateSecureToken, generateTokenExpiry, sanitizeUser } from '@/lib/auth'
import { sendVerificationEmail } from '@/lib/mailer'
import { getClientIP, getUserAgent } from '@/lib/auth-middleware'
import { registrationLimiter, generateRateLimitKey, getClientIdentifier, logSecurityEvent } from '@/lib/rate-limiter'
import type { RegisterRequest } from '@/lib/types'

export async function POST(request: NextRequest) {
  try {
    const clientIp = getClientIP(request)
    const userAgent = getUserAgent(request)

    // Rate limiting: 5 registrations per hour per IP
    const rateLimitKey = generateRateLimitKey('register', clientIp)
    const rateLimitResult = registrationLimiter.check(rateLimitKey)

    if (!rateLimitResult.allowed) {
      logSecurityEvent('registration_rate_limit_exceeded', {
        ip: clientIp,
        userAgent,
        remaining: rateLimitResult.remaining,
        resetTime: new Date(rateLimitResult.resetTime).toISOString()
      })

      return NextResponse.json(
        {
          error: 'Too many registration attempts. Please try again later.',
          retryAfter: Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000)
        },
        {
          status: 429,
          headers: {
            'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()
          }
        }
      )
    }

    const body: RegisterRequest = await request.json()
    const { email, password, displayName } = body

    // Validate input
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Validate email format
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate password strength
    const passwordValidation = isValidPassword(password)
    if (!passwordValidation.valid) {
      return NextResponse.json(
        { error: 'Password does not meet requirements', details: passwordValidation.errors },
        { status: 400 }
      )
    }

    // Check if user already exists
    const existingUser = await users.getByEmail(email.toLowerCase())
    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      )
    }

    // Hash password
    const passwordHash = await hashPassword(password)

    // Create user
    const newUser = await users.create({
      email: email.toLowerCase(),
      passwordHash,
      displayName: displayName || null,
      role: 'user',
      emailVerified: false,
      isActive: true,
      privacySettings: {},
      preferences: {},
    })

    // Generate email verification token
    const verificationToken = generateSecureToken()
    const expiresAt = generateTokenExpiry(24) // 24 hours

    await emailVerificationTokens.create({
      userId: newUser.id,
      token: verificationToken,
      expiresAt: expiresAt.toISOString(),
      usedAt: undefined,
    })

    // Send verification email
    try {
      await sendVerificationEmail(newUser.email, verificationToken)
    } catch (emailError) {
      console.error('Failed to send verification email:', emailError)
      // Continue with registration even if email fails
      // User can request resend later
    }

    // Log the registration
    await auditLogs.create({
      userId: newUser.id,
      action: 'user_registered',
      resourceType: 'user',
      resourceId: newUser.id,
      ipAddress: clientIp,
      userAgent,
      details: { email: newUser.email },
    })

    // Return success (don't include sensitive data)
    return NextResponse.json({
      message: 'User registered successfully. Please check your email for verification.',
      user: sanitizeUser(newUser),
      verificationRequired: true,
    }, { status: 201 })

  } catch (error) {
    console.error('Registration error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}