import { NextRequest, NextResponse } from 'next/server'
import { users, auditLogs } from '@/lib/database'
import { withAuth, getClientIP, getUserAgent } from '@/lib/auth-middleware'
import { sanitizeUser } from '@/lib/auth'

export const GET = withAuth(async (request: NextRequest, { user, userId }) => {
  try {
    // Get all users (admin only)
    const allUsers = await users.getAll()
    const sanitizedUsers = allUsers.map(sanitizeUser)

    // Log admin action
    await auditLogs.create({
      userId,
      action: 'admin_list_users',
      resourceType: 'user',
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request),
      details: { userCount: allUsers.length },
    })

    return NextResponse.json(sanitizedUsers)
  } catch (error) {
    console.error('Error fetching users:', error)
    return NextResponse.json({ error: 'Failed to fetch users' }, { status: 500 })
  }
}, { requiredRoles: ['admin'] })

export const POST = withAuth(async (request: NextRequest, { user, userId }) => {
  try {
    const data = await request.json()

    // Admin can create users with any role
    const newUser = await users.create({
      email: data.email.toLowerCase(),
      passwordHash: data.passwordHash, // Should be pre-hashed
      displayName: data.displayName,
      role: data.role || 'user',
      emailVerified: data.emailVerified || false,
      isActive: data.isActive !== undefined ? data.isActive : true,
      privacySettings: data.privacySettings || {},
      preferences: data.preferences || {},
    })

    // Log admin action
    await auditLogs.create({
      userId,
      action: 'admin_create_user',
      resourceType: 'user',
      resourceId: newUser.id,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request),
      details: {
        createdUserEmail: newUser.email,
        createdUserRole: newUser.role
      },
    })

    return NextResponse.json(sanitizeUser(newUser), { status: 201 })
  } catch (error) {
    console.error('Error creating user:', error)
    return NextResponse.json({ error: 'Failed to create user' }, { status: 500 })
  }
}, { requiredRoles: ['admin'] })