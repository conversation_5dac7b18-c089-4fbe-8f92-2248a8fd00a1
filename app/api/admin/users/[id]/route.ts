import { NextRequest, NextResponse } from 'next/server'
import { users, auditLogs } from '@/lib/database'
import { withAuth, getClientIP, getUserAgent } from '@/lib/auth-middleware'
import { sanitizeUser } from '@/lib/auth'

export const GET = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const targetUser = await users.getById(params.id)
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Log admin action
    await auditLogs.create({
      userId,
      action: 'admin_view_user',
      resourceType: 'user',
      resourceId: params.id,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request),
      details: { viewedUserEmail: targetUser.email },
    })

    return NextResponse.json(sanitizeUser(targetUser))
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json({ error: 'Failed to fetch user' }, { status: 500 })
  }
}, { requiredRoles: ['admin'] })

export const PUT = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const targetUser = await users.getById(params.id)
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const data = await request.json()

    // Prevent admin from demoting themselves
    if (params.id === userId && data.role && data.role !== 'admin') {
      return NextResponse.json({ error: 'Cannot change your own admin role' }, { status: 400 })
    }

    // Prevent admin from deactivating themselves
    if (params.id === userId && data.isActive === false) {
      return NextResponse.json({ error: 'Cannot deactivate your own account' }, { status: 400 })
    }

    const updatedUser = await users.update(params.id, data)
    if (!updatedUser) {
      return NextResponse.json({ error: 'Failed to update user' }, { status: 500 })
    }

    // Log admin action
    await auditLogs.create({
      userId,
      action: 'admin_update_user',
      resourceType: 'user',
      resourceId: params.id,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request),
      details: {
        updatedUserEmail: updatedUser.email,
        changes: Object.keys(data)
      },
    })

    return NextResponse.json(sanitizeUser(updatedUser))
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json({ error: 'Failed to update user' }, { status: 500 })
  }
}, { requiredRoles: ['admin'] })

export const DELETE = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    // Prevent admin from deleting themselves
    if (params.id === userId) {
      return NextResponse.json({ error: 'Cannot delete your own account' }, { status: 400 })
    }

    const targetUser = await users.getById(params.id)
    if (!targetUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const deleted = await users.delete(params.id)
    if (!deleted) {
      return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 })
    }

    // Log admin action
    await auditLogs.create({
      userId,
      action: 'admin_delete_user',
      resourceType: 'user',
      resourceId: params.id,
      ipAddress: getClientIP(request),
      userAgent: getUserAgent(request),
      details: { deletedUserEmail: targetUser.email },
    })

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error deleting user:', error)
    return NextResponse.json({ error: 'Failed to delete user' }, { status: 500 })
  }
}, { requiredRoles: ['admin'] })