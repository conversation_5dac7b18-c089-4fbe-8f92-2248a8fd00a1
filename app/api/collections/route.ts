import { type NextRequest, NextResponse } from "next/server"
import { collections } from "@/lib/database"
import { withAuth } from "@/lib/auth-middleware"
import type { Collection } from "@/lib/types"

export const GET = withAuth(async (request: NextRequest, { user, userId }) => {
  try {
    // Get collections for the authenticated user
    const userCollections = await collections.getByUserId(userId)
    return NextResponse.json(userCollections)
  } catch (error) {
    console.error("Error fetching collections:", error)
    return NextResponse.json({ error: "Failed to fetch collections" }, { status: 500 })
  }
}, { allowUnverified: true })

export const POST = withAuth(async (request: NextRequest, { user, userId }) => {
  try {
    const data = await request.json()

    const collection: Collection = {
      id: crypto.randomUUID(),
      name: data.name,
      paperIds: [],
      userId: userId, // Associate collection with authenticated user
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    const createdCollection = await collections.create(collection)
    return NextResponse.json(createdCollection)
  } catch (error) {
    console.error("Error creating collection:", error)
    return NextResponse.json({ error: "Failed to create collection" }, { status: 500 })
  }
}, { allowUnverified: true })
