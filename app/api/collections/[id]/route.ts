import { type NextRequest, NextResponse } from "next/server"
import { collections } from "@/lib/database"
import { withAuth, canAccessResource } from "@/lib/auth-middleware"

export const GET = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const collection = await collections.getById(params.id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    // Check if user can access this collection
    if (!canAccessResource(user, collection.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    return NextResponse.json(collection)
  } catch (error) {
    console.error("Error fetching collection:", error)
    return NextResponse.json({ error: "Failed to fetch collection" }, { status: 500 })
  }
}, { allowUnverified: true })

export const PUT = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const collection = await collections.getById(params.id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    // Check if user can modify this collection
    if (!canAccessResource(user, collection.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    const data = await request.json()
    const updated = await collections.update(params.id, {
      ...data,
      updatedAt: new Date().toISOString(),
    })

    return NextResponse.json(updated)
  } catch (error) {
    console.error("Error updating collection:", error)
    return NextResponse.json({ error: "Failed to update collection" }, { status: 500 })
  }
}, { allowUnverified: true })

export const DELETE = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const collection = await collections.getById(params.id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    // Check if user can delete this collection
    if (!canAccessResource(user, collection.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    const deleted = await collections.delete(params.id)
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting collection:", error)
    return NextResponse.json({ error: "Failed to delete collection" }, { status: 500 })
  }
}, { allowUnverified: true })
