import { type NextRequest, NextResponse } from "next/server"
import { collections } from "@/lib/database"

export async function POST(request: NextRequest, { params }: { params: { id: string; paperId: string } }) {
  try {
    const collection = await collections.getById(params.id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    if (!collection.paperIds.includes(params.paperId)) {
      const updated = await collections.update(params.id, {
        paperIds: [...collection.paperIds, params.paperId],
      })
      return NextResponse.json(updated)
    }

    return NextResponse.json(collection)
  } catch (error) {
    console.error("Error adding paper to collection:", error)
    return NextResponse.json({ error: "Failed to add paper to collection" }, { status: 500 })
  }
}

export async function DELETE(request: NextRequest, { params }: { params: { id: string; paperId: string } }) {
  try {
    const collection = await collections.getById(params.id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    const updated = await collections.update(params.id, {
      paperIds: collection.paperIds.filter((id) => id !== params.paperId),
    })

    return NextResponse.json(updated)
  } catch (error) {
    console.error("Error removing paper from collection:", error)
    return NextResponse.json({ error: "Failed to remove paper from collection" }, { status: 500 })
  }
}
