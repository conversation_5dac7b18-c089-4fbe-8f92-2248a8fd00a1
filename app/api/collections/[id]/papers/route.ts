import { type NextRequest, NextResponse } from "next/server"
import { collections, papers } from "@/lib/database"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const collection = await collections.getById(params.id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    const collectionPapers = await Promise.all(
      collection.paperIds.map(async (id) => await papers.getById(id))
    )

    return NextResponse.json(collectionPapers.filter(Boolean))
  } catch (error) {
    console.error("Error fetching collection papers:", error)
    return NextResponse.json({ error: "Failed to fetch collection papers" }, { status: 500 })
  }
}

export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { paperIds } = await request.json()

    if (!Array.isArray(paperIds) || paperIds.length === 0) {
      return NextResponse.json({ error: "paperIds must be a non-empty array" }, { status: 400 })
    }

    const collection = await collections.getById(params.id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    // Verify all papers exist
    const paperChecks = await Promise.all(
      paperIds.map(async (paperId) => {
        const paper = await papers.getById(paperId)
        return { paperId, exists: !!paper }
      })
    )

    const invalidPapers = paperChecks.filter(check => !check.exists)
    if (invalidPapers.length > 0) {
      return NextResponse.json({
        error: "Some papers not found",
        invalidPapers: invalidPapers.map(p => p.paperId)
      }, { status: 400 })
    }

    // Add papers to collection (avoid duplicates)
    const existingPaperIds = new Set(collection.paperIds)
    const newPaperIds = paperIds.filter(id => !existingPaperIds.has(id))
    const updatedPaperIds = [...collection.paperIds, ...newPaperIds]

    const updatedCollection = await collections.update(params.id, {
      paperIds: updatedPaperIds
    })

    return NextResponse.json({
      message: "Papers added to collection",
      collection: updatedCollection,
      addedCount: newPaperIds.length,
      skippedCount: paperIds.length - newPaperIds.length
    })
  } catch (error) {
    console.error("Error adding papers to collection:", error)
    return NextResponse.json({ error: "Failed to add papers to collection" }, { status: 500 })
  }
}
