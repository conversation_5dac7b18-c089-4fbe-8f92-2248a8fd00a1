import { type NextRequest, NextResponse } from "next/server"
import { collections, notes } from "@/lib/database"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const collection = await collections.getById(params.id)
    if (!collection) {
      return NextResponse.json({ error: "Collection not found" }, { status: 404 })
    }

    const collectionNotes = await Promise.all(
      collection.paperIds.map(async (paperId) => await notes.getByPaperId(paperId))
    )

    return NextResponse.json(collectionNotes.filter(Boolean))
  } catch (error) {
    console.error("Error fetching collection notes:", error)
    return NextResponse.json({ error: "Failed to fetch collection notes" }, { status: 500 })
  }
}
