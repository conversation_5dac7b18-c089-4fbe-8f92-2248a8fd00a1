import { NextRequest, NextResponse } from "next/server"
import { papers } from "@/lib/database"
import { withAuth, canAccessResource } from "@/lib/auth-middleware"

export const GET = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const paper = await papers.getById(params.id)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 })
    }

    // Check if user can access this paper
    if (!canAccessResource(user, paper.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    return NextResponse.json(paper)
  } catch (error) {
    console.error("Error fetching paper:", error)
    return NextResponse.json({ error: "Failed to fetch paper" }, { status: 500 })
  }
}, { allowUnverified: true })

export const PUT = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const paper = await papers.getById(params.id)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 })
    }

    // Check if user can modify this paper
    if (!canAccessResource(user, paper.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    const data = await request.json()
    const updated = await papers.update(params.id, {
      ...data,
      updatedAt: new Date().toISOString(),
    })

    return NextResponse.json(updated)
  } catch (error) {
    console.error("Error updating paper:", error)
    return NextResponse.json({ error: "Failed to update paper" }, { status: 500 })
  }
}, { allowUnverified: true })

export const DELETE = withAuth(async (request: NextRequest, { user, userId }, { params }: { params: { id: string } }) => {
  try {
    const paper = await papers.getById(params.id)
    if (!paper) {
      return NextResponse.json({ error: "Paper not found" }, { status: 404 })
    }

    // Check if user can delete this paper
    if (!canAccessResource(user, paper.userId)) {
      return NextResponse.json({ error: "Access denied" }, { status: 403 })
    }

    const deleted = await papers.delete(params.id)
    // Also delete associated note and reviews (handled by CASCADE in database)
    // The database foreign key constraints will automatically delete related records

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting paper:", error)
    return NextResponse.json({ error: "Failed to delete paper" }, { status: 500 })
  }
}, { allowUnverified: true })
