import { type NextRequest, NextResponse } from "next/server"
import { collections } from "@/lib/database"

export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const allCollections = await collections.getAll()
    const paperCollections = allCollections.filter((c) => c.paperIds.includes(params.id))
    return NextResponse.json(paperCollections)
  } catch (error) {
    console.error("Error fetching paper collections:", error)
    return NextResponse.json({ error: "Failed to fetch paper collections" }, { status: 500 })
  }
}
