import { NextRequest, NextResponse } from "next/server"
import { findBestMatch, getPaperByDOI, convertSemanticScholarPaper } from "@/lib/semantic-scholar"

export async function POST(request: NextRequest) {
  try {
    const { title, authors, doi } = await request.json()
    
    if (!title && !doi) {
      return NextResponse.json({ error: "Title or DOI is required" }, { status: 400 })
    }
    
    console.log('Enriching paper:', { title, authors, doi })
    
    let semanticScholarPaper = null
    
    // Try to find by DOI first if available
    if (doi) {
      semanticScholarPaper = await getPaperByDOI(doi)
    }
    
    // If not found by DOI, try searching by title
    if (!semanticScholarPaper && title) {
      semanticScholarPaper = await findBestMatch(title, authors)
    }
    
    if (!semanticScholarPaper) {
      return NextResponse.json({ 
        message: "Paper not found in Semantic Scholar",
        enriched: false,
        metadata: {}
      })
    }
    
    // Convert to our format
    const enrichedMetadata = convertSemanticScholarPaper(semanticScholarPaper)
    
    console.log('Successfully enriched paper with metadata:', {
      citationCount: enrichedMetadata.citationCount,
      referenceCount: enrichedMetadata.referenceCount,
      doi: enrichedMetadata.doi
    })
    
    return NextResponse.json({
      message: "Paper enriched successfully",
      enriched: true,
      metadata: enrichedMetadata,
      source: "Semantic Scholar"
    })
    
  } catch (error) {
    console.error("Error enriching paper:", error)
    return NextResponse.json({ 
      error: "Failed to enrich paper metadata",
      enriched: false,
      metadata: {}
    }, { status: 500 })
  }
}
