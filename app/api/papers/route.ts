import { NextRequest, NextResponse } from "next/server"
import { papers, reviews } from "@/lib/database"
import { withAuth } from "@/lib/auth-middleware"
import type { Paper } from "@/lib/types"

export const GET = withAuth(async (request: NextRequest, { user, userId }) => {
  try {
    // Get papers for the authenticated user
    const userPapers = await papers.getByUserId(userId)
    return NextResponse.json(userPapers)
  } catch (error) {
    console.error("Error fetching papers:", error)
    return NextResponse.json({ error: "Failed to fetch papers" }, { status: 500 })
  }
}, { allowUnverified: true })

export const POST = withAuth(async (request: NextRequest, { user, userId }) => {
  try {
    const data = await request.json()

    const paper: Paper = {
      id: crypto.randomUUID(),
      title: data.title,
      authors: data.authors || [],
      venue: data.venue,
      year: data.year,
      doi: data.doi,
      url: data.url,
      abstract: data.abstract,
      citationCount: data.citationCount,
      referenceCount: data.referenceCount,
      publicationDate: data.publicationDate,
      journal: data.journal,
      volume: data.volume,
      issue: data.issue,
      pages: data.pages,
      tags: data.tags || [],
      starred: false,
      userId: userId, // Associate paper with authenticated user
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    const createdPaper = await papers.create(paper)

    // Automatically create a review entry for the new paper
    try {
      const initialReview = {
        paperId: paper.id,
        ease: 2.5,
        nextDue: new Date().toISOString(), // Due immediately for first review
        lastInterval: 1
      }
      await reviews.create(initialReview)
    } catch (error) {
      console.error("Failed to create initial review entry:", error)
      // Don't fail the paper creation if review creation fails
    }

    return NextResponse.json(createdPaper)
  } catch (error) {
    console.error("Error creating paper:", error)
    return NextResponse.json({ error: "Failed to create paper" }, { status: 500 })
  }
}, { allowUnverified: true })
