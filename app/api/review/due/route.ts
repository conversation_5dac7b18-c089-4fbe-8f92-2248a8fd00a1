import { NextResponse } from "next/server"
import { reviews, papers, notes } from "@/lib/database"

export async function GET() {
  try {
    console.log("Fetching due reviews...")
    const now = new Date().toISOString()
    console.log("Current time:", now)

    const allReviews = await reviews.getAll()
    console.log("All reviews:", allReviews.length)

    const dueReviews = allReviews.filter((review) => review.nextDue <= now)
    console.log("Due reviews:", dueReviews.length)

    // Get papers and notes for due reviews
    const duePapers = await Promise.all(
      dueReviews.slice(0, 5).map(async (review) => {
        try {
          const paper = await papers.getById(review.paperId)
          const note = await notes.getByPaperId(review.paperId)
          return paper ? { ...paper, review, note } : null
        } catch (error) {
          console.error(`Error fetching paper ${review.paperId}:`, error)
          return null
        }
      })
    )

    const result = duePapers.filter(Boolean)
    console.log("Returning due papers:", result.length)
    return NextResponse.json(result)
  } catch (error) {
    console.error("Error fetching due reviews:", error)
    // Return empty array instead of error to prevent client-side crashes
    return NextResponse.json([])
  }
}
