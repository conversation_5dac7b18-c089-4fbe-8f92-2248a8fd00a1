import { type NextRequest, NextResponse } from "next/server"
import { reviews } from "@/lib/database"

export async function POST(request: NextRequest, { params }: { params: { paperId: string } }) {
  try {
    const { gotIt } = await request.json()

    let review = await reviews.getByPaperId(params.paperId)

  if (!review) {
    // Create new review
    review = {
      paperId: params.paperId,
      ease: 2.5,
      nextDue: new Date().toISOString(),
    }
  }

  // Enhanced spaced repetition algorithm (based on SM-2)
  const now = new Date()
  let interval: number

  if (gotIt) {
    // Successful review - increase interval
    if (review.ease < 1.3) {
      // First successful review after failure
      interval = 1
    } else if (review.ease < 2.0) {
      // Second successful review
      interval = 6
    } else {
      // Subsequent reviews - exponential growth with ease factor
      const lastInterval = review.lastInterval || 1
      interval = Math.round(lastInterval * review.ease)
    }

    // Increase ease factor (max 3.0)
    review.ease = Math.min(3.0, review.ease + 0.1)
    review.lastInterval = interval
  } else {
    // Failed review - reset to short interval
    interval = 1
    review.ease = Math.max(1.3, review.ease - 0.2)
    review.lastInterval = 1
  }

  // Add some randomization to avoid clustering (±20%)
  const randomFactor = 0.8 + Math.random() * 0.4
  interval = Math.max(1, Math.round(interval * randomFactor))

  const nextDue = new Date(now.getTime() + interval * 24 * 60 * 60 * 1000)
  review.nextDue = nextDue.toISOString()

    const existingReview = await reviews.getByPaperId(params.paperId)
    let updatedReview

    if (existingReview) {
      updatedReview = await reviews.update(params.paperId, review)
    } else {
      updatedReview = await reviews.create(review)
    }

    return NextResponse.json(updatedReview)
  } catch (error) {
    console.error("Error updating review:", error)
    return NextResponse.json({ error: "Failed to update review" }, { status: 500 })
  }
}
