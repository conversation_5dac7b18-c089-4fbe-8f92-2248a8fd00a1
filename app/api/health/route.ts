import { NextRequest, NextResponse } from 'next/server'
import { query } from '@/lib/db'
import { checkSMTPHealth } from '@/lib/mailer'
import { runStartupValidation, getSystemHealthSummary } from '@/lib/startup-checks'

interface HealthCheck {
  service: string
  status: 'healthy' | 'unhealthy'
  message?: string
  responseTime?: number
  details?: any
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const detailed = searchParams.get('detailed') === 'true'

  if (detailed) {
    // Return comprehensive startup validation results
    try {
      const validation = await runStartupValidation()

      return NextResponse.json({
        status: validation.success ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        summary: {
          totalChecks: validation.checks.length,
          passed: validation.checks.filter(c => c.status === 'pass').length,
          warnings: validation.warnings.length,
          errors: validation.errors.length,
        },
        checks: validation.checks,
        errors: validation.errors,
        warnings: validation.warnings,
        version: process.env.npm_package_version || 'unknown',
        environment: process.env.NODE_ENV || 'unknown',
      }, {
        status: validation.success ? 200 : 503,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0',
        },
      })
    } catch (error) {
      return NextResponse.json({
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Health check failed',
        version: process.env.npm_package_version || 'unknown',
        environment: process.env.NODE_ENV || 'unknown',
      }, { status: 503 })
    }
  }

  // Standard health check (faster, for monitoring)
  const checks: HealthCheck[] = []
  let overallHealthy = true

  // Database health check
  const dbStart = Date.now()
  try {
    await query('SELECT 1')
    checks.push({
      service: 'database',
      status: 'healthy',
      responseTime: Date.now() - dbStart,
    })
  } catch (error) {
    overallHealthy = false
    checks.push({
      service: 'database',
      status: 'unhealthy',
      message: error instanceof Error ? error.message : 'Database connection failed',
      responseTime: Date.now() - dbStart,
    })
  }

  // SMTP health check
  const smtpStart = Date.now()
  try {
    const smtpResult = await checkSMTPHealth()
    checks.push({
      service: 'smtp',
      status: smtpResult.healthy ? 'healthy' : 'unhealthy',
      message: smtpResult.error,
      responseTime: Date.now() - smtpStart,
    })

    if (!smtpResult.healthy) {
      overallHealthy = false
    }
  } catch (error) {
    overallHealthy = false
    checks.push({
      service: 'smtp',
      status: 'unhealthy',
      message: error instanceof Error ? error.message : 'SMTP check failed',
      responseTime: Date.now() - smtpStart,
    })
  }

  // Schema health check (quick check for critical tables)
  try {
    const schemaCheck = await query(`
      SELECT COUNT(*) as table_count
      FROM information_schema.tables
      WHERE table_name IN ('users', 'papers', 'notes', 'collections')
    `)

    const tableCount = parseInt(schemaCheck.rows[0].table_count)
    if (tableCount === 4) {
      checks.push({
        service: 'schema',
        status: 'healthy',
        message: 'Core database tables present',
        details: { coreTablesFound: tableCount },
      })
    } else {
      overallHealthy = false
      checks.push({
        service: 'schema',
        status: 'unhealthy',
        message: `Missing core tables (found ${tableCount}/4)`,
        details: { coreTablesFound: tableCount },
      })
    }
  } catch (error) {
    overallHealthy = false
    checks.push({
      service: 'schema',
      status: 'unhealthy',
      message: 'Schema validation failed',
      details: { error: error instanceof Error ? error.message : 'Unknown error' },
    })
  }

  // Application health check
  checks.push({
    service: 'application',
    status: 'healthy',
    message: 'Application is running',
    details: {
      uptime: process.uptime(),
      nodeVersion: process.version,
      platform: process.platform,
    },
  })

  const response = {
    status: overallHealthy ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    checks,
    summary: {
      total: checks.length,
      healthy: checks.filter(c => c.status === 'healthy').length,
      unhealthy: checks.filter(c => c.status === 'unhealthy').length,
    },
    version: process.env.npm_package_version || 'unknown',
    environment: process.env.NODE_ENV || 'unknown',
    endpoints: {
      detailed: '/api/health?detailed=true',
      standard: '/api/health',
    },
  }

  return NextResponse.json(response, {
    status: overallHealthy ? 200 : 503,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0',
    },
  })
}
