"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Plus, <PERSON><PERSON><PERSON>, Book<PERSON>pen<PERSON>heck, Presentation, Trash2 } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Input } from "@/components/ui/input"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { useToast } from "@/hooks/use-toast"
import { ProtectedRoute } from "@/components/auth/ProtectedRoute"
import { AuthModal } from "@/components/auth/AuthModal"
import type { Collection } from "@/lib/types"

function CollectionsPageContent() {
  const [collections, setCollections] = useState<Collection[]>([])
  const [newCollectionName, setNewCollectionName] = useState("")
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [reviewStatuses, setReviewStatuses] = useState<Record<string, {
    inReview: number
    total: number
    percentage: number
    due: number
  }>>({})
  const { toast } = useToast()

  useEffect(() => {
    fetchCollections()
  }, [])

  useEffect(() => {
    if (collections.length > 0) {
      fetchAllReviewStatuses()
    }
  }, [collections])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle shortcuts when typing in inputs
      const target = e.target as HTMLElement
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
        return
      }

      // Handle keyboard shortcuts
      switch (e.key.toLowerCase()) {
        case "n":
          e.preventDefault()
          setIsDialogOpen(true)
          break
        case "escape":
          e.preventDefault()
          setIsDialogOpen(false)
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [])

  const fetchCollections = async () => {
    try {
      const response = await fetch("/api/collections")
      const data = await response.json()
      setCollections(data)
    } catch (error) {
      console.error("Failed to fetch collections:", error)
    }
  }

  const fetchAllReviewStatuses = async () => {
    try {
      const statuses = await Promise.all(
        collections.map(async (collection) => {
          try {
            const response = await fetch(`/api/collections/${collection.id}/review`)
            if (response.ok) {
              const data = await response.json()
              return {
                id: collection.id,
                status: {
                  inReview: data.inReview,
                  total: data.totalPapers,
                  percentage: data.progress.percentage,
                  due: data.due
                }
              }
            }
            return { id: collection.id, status: null }
          } catch (error) {
            console.error(`Failed to fetch review status for collection ${collection.id}:`, error)
            return { id: collection.id, status: null }
          }
        })
      )

      const statusMap = statuses.reduce((acc, { id, status }) => {
        if (status) {
          acc[id] = status
        }
        return acc
      }, {} as Record<string, any>)

      setReviewStatuses(statusMap)
    } catch (error) {
      console.error("Failed to fetch review statuses:", error)
    }
  }

  const createCollection = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!newCollectionName.trim()) return

    try {
      const response = await fetch("/api/collections", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: newCollectionName }),
      })

      if (response.ok) {
        toast({ title: "Collection created" })
        setNewCollectionName("")
        setIsDialogOpen(false)
        fetchCollections()
      }
    } catch (error) {
      toast({ title: "Failed to create collection", variant: "destructive" })
    }
  }

  const deleteCollection = async (collectionId: string, collectionName: string) => {
    if (!confirm(`Are you sure you want to delete "${collectionName}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/collections/${collectionId}`, { method: "DELETE" })
      if (response.ok) {
        toast({ title: "Collection deleted successfully" })
        fetchCollections() // Refresh the list
      } else {
        throw new Error("Failed to delete collection")
      }
    } catch (error) {
      console.error("Failed to delete collection:", error)
      toast({ title: "Failed to delete collection", variant: "destructive" })
    }
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <h1 className="text-2xl font-bold">Collections</h1>
          <div className="flex-1" />
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                New Collection
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Collection</DialogTitle>
              </DialogHeader>
              <form onSubmit={createCollection} className="space-y-4">
                <Input
                  value={newCollectionName}
                  onChange={(e) => setNewCollectionName(e.target.value)}
                  placeholder="Collection name..."
                  autoFocus
                />
                <Button type="submit" className="w-full">
                  Create Collection
                </Button>
              </form>
            </DialogContent>
          </Dialog>
        </div>
      </header>

      <div className="flex-1 overflow-auto p-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {collections.map((collection) => (
            <Card
              key={collection.id}
              className="hover:shadow-md transition-shadow focus-within:ring-2 focus-within:ring-blue-500"
              tabIndex={0}
              onKeyDown={(e) => {
                switch (e.key.toLowerCase()) {
                  case "enter":
                    e.preventDefault()
                    window.location.href = `/collections/${collection.id}`
                    break
                  case "p":
                    e.preventDefault()
                    window.location.href = `/present/${collection.id}`
                    break
                  case "delete":
                  case "backspace":
                    if (e.shiftKey) {
                      e.preventDefault()
                      deleteCollection(collection.id, collection.name)
                    }
                    break
                }
              }}
            >
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {reviewStatuses[collection.id]?.inReview > 0 ? (
                      <BookOpenCheck className="h-5 w-5 text-green-600" />
                    ) : (
                      <BookOpen className="h-5 w-5" />
                    )}
                    {collection.name}
                  </CardTitle>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation()
                      deleteCollection(collection.id, collection.name)
                    }}
                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
                {reviewStatuses[collection.id]?.inReview > 0 && (
                  <div className="mt-2 text-sm text-muted-foreground">
                    <div className="flex items-center justify-between">
                      <span>
                        {reviewStatuses[collection.id].inReview} of {reviewStatuses[collection.id].total} in review
                      </span>
                      {reviewStatuses[collection.id].due > 0 && (
                        <span className="text-orange-600 font-medium">
                          {reviewStatuses[collection.id].due} due
                        </span>
                      )}
                    </div>
                    <div className="mt-1 w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                      <div
                        className="bg-green-600 h-1 rounded-full transition-all duration-300"
                        style={{ width: `${reviewStatuses[collection.id].percentage}%` }}
                      />
                    </div>
                  </div>
                )}
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">{collection.paperIds.length} papers</p>
                <div className="flex gap-2">
                  <Link href={`/collections/${collection.id}`} className="flex-1">
                    <Button variant="outline" className="w-full bg-transparent">
                      View
                    </Button>
                  </Link>
                  <Link href={`/present/${collection.id}`}>
                    <Button variant="outline" size="sm">
                      <Presentation className="h-4 w-4" />
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

export default function CollectionsPage() {
  const [authModalOpen, setAuthModalOpen] = useState(false)

  return (
    <ProtectedRoute
      onAuthRequired={() => setAuthModalOpen(true)}
      requireEmailVerification={false}
    >
      <CollectionsPageContent />
      <AuthModal
        isOpen={authModalOpen}
        onClose={() => setAuthModalOpen(false)}
      />
    </ProtectedRoute>
  )
}
