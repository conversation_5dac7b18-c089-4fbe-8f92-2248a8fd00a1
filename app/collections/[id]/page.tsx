"use client"

import { useState, useEffect } from "react"
import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation"
import { <PERSON><PERSON>eft, Presentation, Trash2, Edit2, <PERSON><PERSON><PERSON>, Book<PERSON>penCheck } from "lucide-react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardHeader, CardTitle } from "@/components/ui/card"
import { SidebarTrigger } from "@/components/ui/sidebar"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import type { Collection, Paper } from "@/lib/types"

export default function CollectionPage() {
  const params = useParams()
  const router = useRouter()
  const { toast } = useToast()
  const [collection, setCollection] = useState<Collection | null>(null)
  const [papers, setPapers] = useState<Paper[]>([])
  const [isEditing, setIsEditing] = useState(false)
  const [editName, setEditName] = useState("")
  const [reviewStatus, setReviewStatus] = useState<{
    inReview: number
    total: number
    percentage: number
    due: number
  } | null>(null)

  const collectionId = params.id as string

  useEffect(() => {
    fetchCollection()
    fetchPapers()
    fetchReviewStatus()
  }, [collectionId])

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle shortcuts when editing collection name
      if (isEditing) return

      // Don't handle shortcuts when typing in inputs
      const target = e.target as HTMLElement
      if (target.tagName === "INPUT" || target.tagName === "TEXTAREA") {
        return
      }

      // Handle keyboard shortcuts
      switch (e.key.toLowerCase()) {
        case "e":
          e.preventDefault()
          setIsEditing(true)
          break
        case "p":
          e.preventDefault()
          window.location.href = `/present/${collectionId}`
          break
        case "escape":
          e.preventDefault()
          router.push("/collections")
          break
        case "r":
          e.preventDefault()
          if (reviewStatus && reviewStatus.inReview > 0) {
            removeCollectionFromReview()
          } else {
            addCollectionToReview()
          }
          break
        case "delete":
        case "backspace":
          if (e.shiftKey) {
            e.preventDefault()
            deleteCollection()
          }
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [isEditing, collectionId, router])

  const fetchCollection = async () => {
    try {
      const response = await fetch(`/api/collections/${collectionId}`)
      if (response.ok) {
        const data = await response.json()
        setCollection(data)
        setEditName(data.name)
      }
    } catch (error) {
      console.error("Failed to fetch collection:", error)
    }
  }

  const fetchPapers = async () => {
    try {
      const response = await fetch(`/api/collections/${collectionId}/papers`)
      if (response.ok) {
        const data = await response.json()
        setPapers(data)
      }
    } catch (error) {
      console.error("Failed to fetch papers:", error)
    }
  }

  const fetchReviewStatus = async () => {
    try {
      const response = await fetch(`/api/collections/${collectionId}/review`)
      if (response.ok) {
        const data = await response.json()
        setReviewStatus({
          inReview: data.inReview,
          total: data.totalPapers,
          percentage: data.progress.percentage,
          due: data.due
        })
      }
    } catch (error) {
      console.error("Failed to fetch review status:", error)
    }
  }

  const addCollectionToReview = async () => {
    try {
      const response = await fetch(`/api/collections/${collectionId}/review`, {
        method: "POST",
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Collection added to review queue",
          description: `${data.successful} papers added successfully`
        })
        fetchReviewStatus() // Refresh status
      } else {
        throw new Error("Failed to add collection to review")
      }
    } catch (error) {
      console.error("Failed to add collection to review:", error)
      toast({ title: "Failed to add collection to review", variant: "destructive" })
    }
  }

  const removeCollectionFromReview = async () => {
    try {
      const response = await fetch(`/api/collections/${collectionId}/review`, {
        method: "DELETE",
      })

      if (response.ok) {
        const data = await response.json()
        toast({
          title: "Collection removed from review queue",
          description: `${data.removed} papers removed`
        })
        fetchReviewStatus() // Refresh status
      } else {
        throw new Error("Failed to remove collection from review")
      }
    } catch (error) {
      console.error("Failed to remove collection from review:", error)
      toast({ title: "Failed to remove collection from review", variant: "destructive" })
    }
  }

  const updateCollectionName = async () => {
    if (!editName.trim()) return

    try {
      const response = await fetch(`/api/collections/${collectionId}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: editName }),
      })

      if (response.ok) {
        setCollection((prev) => (prev ? { ...prev, name: editName } : null))
        setIsEditing(false)
        toast({ title: "Collection updated" })
      }
    } catch (error) {
      toast({ title: "Failed to update collection", variant: "destructive" })
    }
  }

  const removePaper = async (paperId: string) => {
    try {
      await fetch(`/api/collections/${collectionId}/papers/${paperId}`, {
        method: "DELETE",
      })
      fetchPapers()
      toast({ title: "Paper removed from collection" })
    } catch (error) {
      toast({ title: "Failed to remove paper", variant: "destructive" })
    }
  }

  const deleteCollection = async () => {
    if (!collection) return

    if (!confirm(`Are you sure you want to delete "${collection.name}"? This action cannot be undone.`)) {
      return
    }

    try {
      const response = await fetch(`/api/collections/${collectionId}`, { method: "DELETE" })
      if (response.ok) {
        toast({ title: "Collection deleted successfully" })
        router.push("/collections")
      } else {
        throw new Error("Failed to delete collection")
      }
    } catch (error) {
      console.error("Failed to delete collection:", error)
      toast({ title: "Failed to delete collection", variant: "destructive" })
    }
  }

  if (!collection) {
    return <div className="p-4">Loading...</div>
  }

  return (
    <div className="flex flex-col h-screen">
      <header className="border-b p-4">
        <div className="flex items-center gap-4">
          <SidebarTrigger />
          <Link href="/collections">
            <Button variant="ghost" size="sm">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
          </Link>
          {isEditing ? (
            <div className="flex items-center gap-2 flex-1">
              <Input
                value={editName}
                onChange={(e) => setEditName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === "Enter") updateCollectionName()
                  if (e.key === "Escape") setIsEditing(false)
                }}
                autoFocus
              />
              <Button onClick={updateCollectionName} size="sm">
                Save
              </Button>
            </div>
          ) : (
            <>
              <h1 className="text-2xl font-bold flex-1">{collection.name}</h1>
              <Button variant="ghost" size="sm" onClick={() => setIsEditing(true)}>
                <Edit2 className="h-4 w-4" />
              </Button>
            </>
          )}
          <Link href={`/present/${collectionId}`}>
            <Button>
              <Presentation className="h-4 w-4 mr-2" />
              Present
            </Button>
          </Link>
          {reviewStatus && reviewStatus.inReview > 0 ? (
            <Button
              variant="outline"
              onClick={removeCollectionFromReview}
              className="text-green-600 border-green-300 hover:bg-green-50"
            >
              <BookOpenCheck className="h-4 w-4 mr-2" />
              In Review ({reviewStatus.inReview}/{reviewStatus.total})
            </Button>
          ) : (
            <Button variant="outline" onClick={addCollectionToReview}>
              <BookOpen className="h-4 w-4 mr-2" />
              Add to Review
            </Button>
          )}
          <Button
            variant="ghost"
            size="sm"
            onClick={deleteCollection}
            className="text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <Trash2 className="h-4 w-4" />
          </Button>
        </div>
      </header>

      <div className="flex-1 overflow-auto p-4">
        {reviewStatus && reviewStatus.inReview > 0 && (
          <div className="mb-4 p-4 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <BookOpenCheck className="h-5 w-5 text-blue-600" />
                <span className="font-medium text-blue-900 dark:text-blue-100">
                  Collection Review Progress
                </span>
              </div>
              <div className="text-sm text-blue-700 dark:text-blue-300">
                {reviewStatus.inReview} of {reviewStatus.total} papers in review queue
                {reviewStatus.due > 0 && (
                  <span className="ml-2 font-medium">• {reviewStatus.due} due now</span>
                )}
              </div>
            </div>
            <div className="mt-2 w-full bg-blue-200 dark:bg-blue-800 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${reviewStatus.percentage}%` }}
              />
            </div>
            <div className="mt-1 text-xs text-blue-600 dark:text-blue-400">
              {reviewStatus.percentage}% complete
            </div>
          </div>
        )}

        <div className="grid gap-4">
          {papers.map((paper) => (
            <Card key={paper.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <CardTitle className="text-lg">
                      <Link href={`/papers/${paper.id}`} className="hover:underline">
                        {paper.title}
                      </Link>
                    </CardTitle>
                    <p className="text-sm text-muted-foreground mt-1">{paper.authors.join(", ")}</p>
                    {paper.venue && (
                      <p className="text-sm text-muted-foreground">
                        {paper.venue} {paper.year && `(${paper.year})`}
                      </p>
                    )}
                  </div>
                  <Button variant="ghost" size="sm" onClick={() => removePaper(paper.id)}>
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
            </Card>
          ))}
          {papers.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">No papers in this collection yet.</div>
          )}
        </div>
      </div>
    </div>
  )
}
