"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { CheckCircle, RotateCcw, ArrowLeft, ArrowRight, X, ExternalLink } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { useToast } from "@/hooks/use-toast"
import type { Paper, Review, Note } from "@/lib/types"

export default function ReviewPresentPage() {
  const router = useRouter()
  const { toast } = useToast()
  const [duePapers, setDuePapers] = useState<(Paper & { review: Review; note?: Note })[]>([])
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    fetchDuePapers()
  }, [])

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      switch (e.key.toLowerCase()) {
        case "arrowleft":
        case "h":
          e.preventDefault()
          prevPaper()
          break
        case "arrowright":
        case "l":
          e.preventDefault()
          nextPaper()
          break
        case "g":
        case "enter":
          e.preventDefault()
          if (currentPaper) {
            updateReview(currentPaper.id, true)
          }
          break
        case "r":
        case " ":
          e.preventDefault()
          if (currentPaper) {
            updateReview(currentPaper.id, false)
          }
          break
        case "escape":
          e.preventDefault()
          router.push("/review")
          break
      }
    }

    window.addEventListener("keydown", handleKeyDown)
    return () => window.removeEventListener("keydown", handleKeyDown)
  }, [currentIndex, duePapers.length])

  const fetchDuePapers = async () => {
    try {
      console.log("Fetching due papers...")
      setIsLoading(true)
      const response = await fetch("/api/review/due")
      console.log("Response status:", response.status)
      if (response.ok) {
        const data = await response.json()
        console.log("Due papers data:", data)
        setDuePapers(Array.isArray(data) ? data : [])
      } else {
        console.error("Failed to fetch due papers:", response.status, response.statusText)
        toast({ title: "Failed to fetch due papers", variant: "destructive" })
        setDuePapers([])
      }
    } catch (error) {
      console.error("Failed to fetch due papers:", error)
      toast({ title: "Failed to fetch due papers", variant: "destructive" })
      setDuePapers([])
    } finally {
      setIsLoading(false)
    }
  }

  const updateReview = async (paperId: string, gotIt: boolean) => {
    try {
      const response = await fetch(`/api/review/${paperId}`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ gotIt }),
      })

      if (response.ok) {
        toast({ 
          title: gotIt ? "Great job! 🎉" : "Marked for review 📚", 
          description: gotIt ? "Paper marked as understood" : "Paper will appear again soon"
        })
        
        // Remove the paper from the current list
        const newDuePapers = duePapers.filter(p => p.id !== paperId)
        setDuePapers(newDuePapers)
        
        // Adjust current index if needed
        if (currentIndex >= newDuePapers.length && newDuePapers.length > 0) {
          setCurrentIndex(newDuePapers.length - 1)
        } else if (newDuePapers.length === 0) {
          // All papers reviewed, show completion
          setCurrentIndex(0)
        }
      } else {
        throw new Error("Failed to update review")
      }
    } catch (error) {
      console.error("Failed to update review:", error)
      toast({ title: "Failed to update review", variant: "destructive" })
    }
  }

  const nextPaper = () => {
    if (currentIndex < duePapers.length - 1) {
      setCurrentIndex(currentIndex + 1)
    }
  }

  const prevPaper = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1)
    }
  }

  const currentPaper = duePapers[currentIndex]

  if (isLoading) {
    return (
      <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading papers for review...</p>
          </div>
        </div>
      </div>
    )
  }

  if (duePapers.length === 0) {
    return (
      <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
        <div className="absolute top-4 right-4">
          <Button variant="ghost" size="sm" onClick={() => router.push("/review")}>
            <X className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center max-w-md">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold mb-2">Batch Review Complete! 🎉</h2>
            <p className="text-muted-foreground mb-4">
              You've successfully completed your review session. All papers have been processed and scheduled for future review.
            </p>
            <div className="space-y-3">
              <Button onClick={() => router.push("/review")} size="lg">
                Back to Review Dashboard
              </Button>
              <Button
                onClick={() => router.push("/papers")}
                variant="outline"
              >
                Browse Papers
              </Button>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (!currentPaper) {
    return (
      <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading review...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header with progress and controls */}
      <div className="bg-white dark:bg-gray-800 border-b p-4">
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <div className="flex items-center gap-4">
            <Button variant="ghost" size="sm" onClick={() => router.push("/review")}>
              <X className="h-4 w-4" />
            </Button>
            <div className="text-sm text-muted-foreground">
              Batch Review Session • Paper {currentIndex + 1} of {duePapers.length}
            </div>
          </div>

          <div className="flex-1 max-w-md mx-8">
            <Progress value={(currentIndex + 1) / duePapers.length * 100} className="h-2" />
            <div className="text-xs text-center mt-1 text-muted-foreground">
              {Math.round(((currentIndex + 1) / duePapers.length) * 100)}% Complete
            </div>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={prevPaper}
              disabled={currentIndex === 0}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={nextPaper}
              disabled={currentIndex === duePapers.length - 1}
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className="flex-1 overflow-auto p-6">
        <div className="max-w-4xl mx-auto">
          <Card className="shadow-lg">
            <CardHeader className="text-center pb-6">
              <CardTitle className="text-3xl leading-tight mb-4">{currentPaper.title}</CardTitle>
              <div className="space-y-2">
                <p className="text-xl text-muted-foreground">{currentPaper.authors.join(", ")}</p>
                {currentPaper.venue && (
                  <p className="text-lg text-muted-foreground">
                    {currentPaper.venue} {currentPaper.year && `(${currentPaper.year})`}
                  </p>
                )}
                <div className="flex justify-center gap-2 mt-4">
                  {currentPaper.tags?.map((tag) => (
                    <Badge key={tag} variant="outline" className="text-sm">
                      {tag}
                    </Badge>
                  )) || []}
                </div>
                
                {/* DOI/URL Links */}
                <div className="flex justify-center gap-4 mt-4">
                  {currentPaper.doi && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(`https://doi.org/${currentPaper.doi}`, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      DOI
                    </Button>
                  )}
                  {currentPaper.url && !currentPaper.doi && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => window.open(currentPaper.url, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Link
                    </Button>
                  )}
                </div>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-6">
              {currentPaper.note && (
                <div className="bg-muted/30 rounded-lg p-6">
                  <h3 className="text-xl font-semibold mb-4 flex items-center gap-2">
                    📝 Your Notes
                  </h3>
                  <div className="space-y-4">
                    {currentPaper.note.bullets?.filter(Boolean).map((bullet, index) => (
                      <div key={index} className="flex items-start gap-3 p-3 bg-background rounded-md">
                        <span className="font-semibold text-primary mt-1 min-w-[24px] text-lg">{index + 1}.</span>
                        <p className="leading-relaxed text-lg">{bullet}</p>
                      </div>
                    )) || []}
                  </div>

                  {currentPaper.note.whyItMatters && (
                    <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/30 rounded-md border-l-4 border-blue-500">
                      <h4 className="font-semibold mb-2 text-blue-900 dark:text-blue-100 text-lg">💡 Why It Matters</h4>
                      <p className="leading-relaxed text-blue-800 dark:text-blue-200 text-lg">{currentPaper.note.whyItMatters}</p>
                    </div>
                  )}
                </div>
              )}

              {!currentPaper.note && (
                <div className="text-center py-8 text-muted-foreground">
                  <p className="text-lg">No notes available for this paper.</p>
                  <p className="text-sm mt-2">Consider adding notes to improve your review experience.</p>
                </div>
              )}

              {/* Action buttons */}
              <div className="space-y-4 pt-6 border-t">
                <div className="flex gap-4">
                  <Button 
                    onClick={() => updateReview(currentPaper.id, true)} 
                    className="flex-1 bg-green-600 hover:bg-green-700 text-lg py-6" 
                    size="lg"
                  >
                    <CheckCircle className="h-6 w-6 mr-3" />
                    Got it!
                    <kbd className="ml-3 px-3 py-2 bg-white/20 rounded text-sm">G</kbd>
                  </Button>
                  <Button
                    onClick={() => updateReview(currentPaper.id, false)}
                    variant="outline"
                    className="flex-1 border-orange-300 text-orange-700 hover:bg-orange-50 text-lg py-6"
                    size="lg"
                  >
                    <RotateCcw className="h-6 w-6 mr-3" />
                    Revisit
                    <kbd className="ml-3 px-3 py-2 bg-muted rounded text-sm">R</kbd>
                  </Button>
                </div>
                
                <div className="text-center text-sm text-muted-foreground bg-muted/30 rounded-lg p-4">
                  <div className="flex items-center justify-center gap-6 flex-wrap">
                    <span><kbd className="px-2 py-1 bg-background rounded text-xs">←/→</kbd> Navigate</span>
                    <span><kbd className="px-2 py-1 bg-background rounded text-xs">G</kbd> Got it</span>
                    <span><kbd className="px-2 py-1 bg-background rounded text-xs">R</kbd> Revisit</span>
                    <span><kbd className="px-2 py-1 bg-background rounded text-xs">ESC</kbd> Exit</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
