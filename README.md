# PaperNugget

A research paper management and review system built with Next.js and PostgreSQL.

## Features

- **Paper Management**: Complete metadata support (title, authors, venue, year, DOI, etc.)
- **DOI Integration**: Automatic paper metadata fetching from CrossRef API
- **Note-taking**: Bullet points and key insights with structured format
- **Collections**: Organize papers into custom collections
- **Spaced Repetition**: Review system for better retention
- **Search & Filter**: Advanced search and filtering capabilities
- **Keyboard Shortcuts**: Full keyboard navigation and shortcuts
- **Paper & Collection Deletion**: Safe deletion with confirmation dialogs
- **User Management**: Complete authentication system with role-based access control
- **Email Verification**: Secure email verification with resend functionality
- **Docker-Only Environment**: Consistent deployment across all environments
- **Responsive Design**: Dark/light theme support with modern UI

## Getting Started

### Prerequisites

- Docker and Docker Compose
- Git

### One-Command Bootstrap (Recommended)

**The fastest way to get PaperNugget running:**

1. Clone the repository:
```bash
git clone <repository-url>
cd papernugget
```

2. Run the bootstrap script:
```bash
./bootstrap.sh
```

Or using Make:
```bash
make bootstrap
```

That's it! The bootstrap script will:
- ✅ Check Docker prerequisites
- ✅ Set up environment configuration
- ✅ Start all Docker services
- ✅ Apply database schema
- ✅ Seed test data including admin user
- ✅ Verify the installation

### Access Points

After bootstrap completes, you can access:
- **Main Application**: http://localhost:3000
- **Email Testing (Mailpit)**: http://localhost:8025
- **Database**: localhost:5432

### Default Admin Account

- **Email**: <EMAIL>
- **Password**: admin123
- ⚠️ **Important**: Change this password immediately after first login!

### Manual Setup (Alternative)

If you prefer manual setup:

```bash
cp .env.example .env
docker compose up --build -d
docker compose exec app npm run migrate
docker compose exec app npm run seed:test-users
```

## Email Verification System

PaperNugget includes a comprehensive email verification system for secure user registration and authentication.

### Environment Configuration

The following environment variables are required for email functionality:

```bash
# Application URL (used for verification links)
APP_URL=http://localhost:3000

# Email sender configuration
EMAIL_FROM=<EMAIL>

# SMTP server configuration
SMTP_HOST=localhost
SMTP_PORT=1025
SMTP_USER=
SMTP_PASS=
SMTP_TLS=false
```

### Local Development with Mailpit

For local development, we use Mailpit to capture and view emails:

1. **Start all services**:
```bash
npm run docker:up
# or manually: docker-compose up -d
```

2. **View emails**: Open http://localhost:8025 to see captured emails

3. **Seed test users** (run inside Docker container):
```bash
docker-compose exec app npm run seed:test-users
```

This creates test users:
- `<EMAIL>` / `TestPassword123!` (verified)
- `<EMAIL>` / `TestPassword123!` (unverified)
- `<EMAIL>` / `AdminPassword123!` (admin, verified)

### User Registration Flow

1. User registers with email and password
2. System creates unverified user account
3. Verification email sent with secure token (24-hour expiry)
4. User clicks verification link to activate account
5. User can now sign in

### Security Features

- **Rate Limiting**:
  - Registration: 5 attempts per hour per IP
  - Resend verification: 1 per minute, 5 per day per email
- **Secure Tokens**: 64-character URL-safe tokens with 24-hour expiry
- **Single-use Tokens**: Tokens are invalidated after use
- **Email Enumeration Protection**: Generic responses prevent email discovery
- **Audit Logging**: All verification events are logged

### Production SMTP Setup

For production, configure a real SMTP service:

```bash
# Example with Gmail
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
SMTP_TLS=true
EMAIL_FROM=<EMAIL>
APP_URL=https://yourdomain.com
```

### Health Checks

Monitor email system health:
- **Endpoint**: `GET /api/health`
- **Checks**: Database connectivity, SMTP server connection
- **Response**: JSON with service status and response times

### Testing

Run email verification tests:

```bash
# All tests
npm test

# Unit tests only
npm run test:unit

# Integration tests only (requires running server)
npm run test:integration
```

### Troubleshooting

**Common Issues:**

1. **SMTP Connection Failed**
   - Check SMTP_HOST and SMTP_PORT
   - Verify firewall/network access
   - For Gmail: Use app passwords, not account password

2. **Emails Not Sending**
   - Check /api/health endpoint
   - Verify EMAIL_FROM format
   - Check server logs for detailed errors

3. **Verification Links Not Working**
   - Ensure APP_URL matches your domain
   - Check token expiry (24 hours)
   - Verify database connectivity

4. **Rate Limiting Issues**
   - Wait for rate limit window to reset
   - Check IP address in logs
   - Adjust rate limits in production if needed

## Keyboard Shortcuts

PaperNugget includes comprehensive keyboard shortcuts for efficient navigation:

### Papers
- **N** - Create new paper
- **S** - Star/unstar paper (on focused card or in paper view)
- **E** - Edit paper (focus title field or navigate to paper)
- **ESC** - Close/back navigation or clear filters
- **Shift + Delete** - Delete paper (with confirmation)
- **1-6** - Focus bullet points (in paper view)

### Collections
- **N** - Create new collection
- **P** - Present collection (on focused card or in collection view)
- **E** - Edit collection name (in collection view)
- **ESC** - Close/back navigation
- **Shift + Delete** - Delete collection (with confirmation)

### Navigation
- **Tab** - Navigate between focusable elements
- **Enter** - Activate focused element

Click the keyboard icon (⌨️) in the sidebar for a complete reference.

## Database

The application uses PostgreSQL with the following tables:
- `papers` - Research paper metadata
- `notes` - Notes associated with papers
- `collections` - Paper collections/groups
- `reviews` - Spaced repetition review data

The database is automatically initialized with sample data when using Docker Compose.

## Environment Variables

- `DATABASE_URL` - PostgreSQL connection string
- `NODE_ENV` - Environment (development/production)
- `PORT` - Application port (default: 3000)
- `POSTGRES_DB` - Database name
- `POSTGRES_USER` - Database user
- `POSTGRES_PASSWORD` - Database password

## Development Guide

### Quick Development Commands

PaperNugget includes a comprehensive set of commands for development and maintenance:

```bash
# Quick start
make bootstrap          # One-command setup (recommended)
make help              # Show all available commands

# Health and diagnostics
make health            # Basic health check
make health-detailed   # Comprehensive health check with detailed output
make email-test        # Test email system configuration
make email-test-send   # Send test verification email

# Testing
make test              # Run all tests (unit + integration)
npm run test:unit      # Run unit tests only
npm run test:integration # Run integration tests only
npm run test:ci        # Run tests for CI environment

# Database operations
make seed              # Seed test data
npm run migrate        # Run database migrations

# Application lifecycle
make start             # Start all services
make stop              # Stop all services
make restart           # Restart all services
make logs              # View application logs

# Maintenance
make clean             # Remove containers and volumes
make reset             # Complete reset (clean + bootstrap)
```

### Development Workflow

1. **Initial Setup**:
   ```bash
   git clone <repository-url>
   cd papernugget
   ./bootstrap.sh        # One-command setup
   ```

2. **Daily Development**:
   ```bash
   make start            # Start services
   # Make your changes
   make test             # Test changes
   make health-detailed  # Verify system health
   ```

3. **Database Schema Changes**:
   ```bash
   # Edit lib/db-migrations.ts
   make clean            # Reset database
   make bootstrap        # Apply new schema
   make test             # Verify tests pass
   ```

4. **Email System Changes**:
   ```bash
   make email-test       # Test email configuration
   make email-test-send  # Send test email
   # Check http://localhost:8025 for emails
   ```

### Testing

PaperNugget includes comprehensive automated testing:

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test API endpoints and database operations
- **End-to-End Tests**: Test complete user workflows
- **Health Checks**: Validate system configuration and connectivity

```bash
# Run all tests
make test

# Run specific test categories
npm run test:unit           # Fast unit tests
npm run test:integration    # Database and API tests
npm run health:detailed     # System health validation
npm run email:test          # Email system tests
```

### Troubleshooting

Common development issues and solutions:

1. **Bootstrap fails**:
   ```bash
   # Check Docker is running
   docker --version
   docker compose version

   # Clean and retry
   make clean
   ./bootstrap.sh
   ```

2. **Tests fail**:
   ```bash
   # Check system health first
   make health-detailed

   # Reset and retry
   make reset
   make test
   ```

3. **Email not working**:
   ```bash
   # Test email system
   make email-test

   # Check Mailpit is running
   curl http://localhost:8025
   ```

4. **Database issues**:
   ```bash
   # Reset database
   make clean
   make bootstrap

   # Check database logs
   docker compose logs db
   ```

## Docker Commands

### Development
```bash
# Start development environment
docker-compose -f docker-compose.dev.yml up --build

# Stop development environment
docker-compose -f docker-compose.dev.yml down

# View logs
docker-compose -f docker-compose.dev.yml logs -f
```

### Production
```bash
# Start production environment
docker-compose up --build -d

# Stop production environment
docker-compose down

# View logs
docker-compose logs -f

# Backup database
docker-compose exec db pg_dump -U papernugget papernugget > backup.sql
```

## Data Persistence

Database data is persisted using Docker volumes:
- Development: `postgres_data_dev`
- Production: `postgres_data`

To reset the database, remove the volume:
```bash
docker-compose down -v
```

## Documentation

- **[API Documentation](docs/API.md)**: Complete REST API reference
- **[Deployment Guide](docs/DEPLOYMENT.md)**: Production deployment instructions
- **[Testing Guide](docs/TESTING.md)**: Testing strategies and running tests
- **[Troubleshooting Guide](docs/TROUBLESHOOTING.md)**: Common issues and solutions

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly:
   ```bash
   make test              # Run all tests
   make health-detailed   # Verify system health
   make email-test        # Test email functionality
   ```
5. Submit a pull request

See [Testing Guide](docs/TESTING.md) for detailed testing instructions.

## License

MIT License
