.PHONY: help bootstrap start stop build clean logs backup restore test

# Default target
help:
	@echo "PaperNugget Commands"
	@echo ""
	@echo "Quick Start:"
	@echo "  make bootstrap - One-command setup (recommended for new installations)"
	@echo ""
	@echo "Application:"
	@echo "  make start     - Start PaperNugget"
	@echo "  make stop      - Stop PaperNugget"
	@echo "  make logs      - View application logs"
	@echo "  make restart   - Restart PaperNugget"
	@echo ""
	@echo "Development:"
	@echo "  make test             - Run all tests"
	@echo "  make health           - Run health check"
	@echo "  make health-detailed  - Run detailed health check"
	@echo "  make email-test       - Test email system"
	@echo "  make email-test-send  - Test email system with test email"
	@echo "  make seed             - Seed test data"
	@echo ""
	@echo "Maintenance:"
	@echo "  make build     - Build images without starting"
	@echo "  make clean     - Remove containers and volumes"
	@echo "  make reset     - Complete reset (clean + bootstrap)"
	@echo "  make backup    - Backup database"
	@echo "  make restore   - Restore database from backup"

# Quick start command
bootstrap:
	@echo "🚀 Running PaperNugget bootstrap..."
	./bootstrap.sh

# Application commands
start:
	@echo "Starting PaperNugget..."
	docker compose up --build -d

stop:
	@echo "Stopping PaperNugget..."
	docker compose down

restart:
	@echo "Restarting PaperNugget..."
	docker compose down
	docker compose up --build -d

logs:
	@echo "Viewing application logs..."
	docker compose logs -f

# Development commands
test:
	@echo "Running tests..."
	docker compose exec app npm test

health:
	@echo "Running health check..."
	docker compose exec app npm run health

health-detailed:
	@echo "Running detailed health check..."
	docker compose exec app npm run health:detailed

email-test:
	@echo "Testing email system..."
	docker compose exec app npm run email:test

email-test-send:
	@echo "Testing email system with test email..."
	docker compose exec app npm run email:test-send

seed:
	@echo "Seeding test data..."
	docker compose exec app npm run seed:test-users

# Build commands
build:
	@echo "Building images..."
	docker compose build

# Maintenance commands
clean:
	@echo "Removing containers and volumes..."
	docker compose down -v --remove-orphans
	docker system prune -f

reset:
	@echo "Performing complete reset..."
	@$(MAKE) clean
	@$(MAKE) bootstrap

backup:
	@echo "Creating database backup..."
	@mkdir -p backups
	docker compose exec -T db pg_dump -U papernugget papernugget > backups/backup_$(shell date +%Y%m%d_%H%M%S).sql
	@echo "Backup created in backups/ directory"

restore:
	@echo "Restoring database from backup..."
	@read -p "Enter backup file path: " backup_file; \
	docker compose exec -T db psql -U papernugget -d papernugget < $$backup_file

# Setup commands
setup:
	@echo "Setting up environment..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "Created .env file from .env.example"; \
		echo "Please review and update the .env file before starting the application"; \
	else \
		echo ".env file already exists"; \
	fi
