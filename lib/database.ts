import { query } from './db'
import type {
  Paper,
  Note,
  Collection,
  Review,
  User,
  UserSession,
  PasswordResetToken,
  EmailVerificationToken,
  AuditLog
} from './types'
// Note: Migrations are now run via startup script, not on import

// Papers CRUD operations
export const papers = {
  getAll: async (): Promise<Paper[]> => {
    try {
      const result = await query('SELECT * FROM papers ORDER BY created_at DESC')
      return result.rows.map(row => ({
        id: row.id,
        title: row.title,
        authors: row.authors,
        venue: row.venue,
        year: row.year,
        doi: row.doi,
        url: row.url,
        abstract: row.abstract,
        citationCount: row.citation_count,
        referenceCount: row.reference_count,
        publicationDate: row.publication_date,
        journal: row.journal,
        volume: row.volume,
        issue: row.issue,
        pages: row.pages,
        tags: row.tags,
        starred: row.starred,
        userId: row.user_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }))
    } catch (error) {
      console.error('Error getting all papers:', error)
      return []
    }
  },

  getByUserId: async (userId: string): Promise<Paper[]> => {
    try {
      const result = await query('SELECT * FROM papers WHERE user_id = $1 ORDER BY created_at DESC', [userId])
      return result.rows.map(row => ({
        id: row.id,
        title: row.title,
        authors: row.authors,
        venue: row.venue,
        year: row.year,
        doi: row.doi,
        url: row.url,
        abstract: row.abstract,
        citationCount: row.citation_count,
        referenceCount: row.reference_count,
        publicationDate: row.publication_date,
        journal: row.journal,
        volume: row.volume,
        issue: row.issue,
        pages: row.pages,
        tags: row.tags,
        starred: row.starred,
        userId: row.user_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }))
    } catch (error) {
      console.error('Error getting papers by user id:', error)
      return []
    }
  },

  getById: async (id: string): Promise<Paper | undefined> => {
    try {
      const result = await query('SELECT * FROM papers WHERE id = $1', [id])
      if (result.rows.length === 0) return undefined

      const row = result.rows[0]
      return {
        id: row.id,
        title: row.title,
        authors: row.authors,
        venue: row.venue,
        year: row.year,
        doi: row.doi,
        url: row.url,
        abstract: row.abstract,
        citationCount: row.citation_count,
        referenceCount: row.reference_count,
        publicationDate: row.publication_date,
        journal: row.journal,
        volume: row.volume,
        issue: row.issue,
        pages: row.pages,
        tags: row.tags,
        starred: row.starred,
        userId: row.user_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }
    } catch (error) {
      console.error('Error getting paper by id:', error)
      return undefined
    }
  },

  create: async (paper: Paper): Promise<Paper> => {
    try {
      const result = await query(
        `INSERT INTO papers (id, title, authors, venue, year, doi, url, abstract, citation_count, reference_count, publication_date, journal, volume, issue, pages, tags, starred, user_id, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20)
         RETURNING *`,
        [
          paper.id,
          paper.title,
          paper.authors,
          paper.venue,
          paper.year,
          paper.doi,
          paper.url,
          paper.abstract,
          paper.citationCount,
          paper.referenceCount,
          paper.publicationDate,
          paper.journal,
          paper.volume,
          paper.issue,
          paper.pages,
          paper.tags,
          paper.starred,
          paper.userId,
          paper.createdAt,
          paper.updatedAt,
        ]
      )
      
      const row = result.rows[0]
      return {
        id: row.id,
        title: row.title,
        authors: row.authors,
        venue: row.venue,
        year: row.year,
        doi: row.doi,
        url: row.url,
        abstract: row.abstract,
        citationCount: row.citation_count,
        referenceCount: row.reference_count,
        publicationDate: row.publication_date,
        journal: row.journal,
        volume: row.volume,
        issue: row.issue,
        pages: row.pages,
        tags: row.tags,
        starred: row.starred,
        userId: row.user_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }
    } catch (error) {
      console.error('Error creating paper:', error)
      throw error
    }
  },

  update: async (id: string, updates: Partial<Paper>): Promise<Paper | null> => {
    try {
      const existing = await papers.getById(id)
      if (!existing) return null

      const updated = { ...existing, ...updates, updatedAt: new Date().toISOString() }
      
      const result = await query(
        `UPDATE papers
         SET title = $2, authors = $3, venue = $4, year = $5, doi = $6, url = $7,
             abstract = $8, citation_count = $9, reference_count = $10, publication_date = $11,
             journal = $12, volume = $13, issue = $14, pages = $15,
             tags = $16, starred = $17, updated_at = $18
         WHERE id = $1
         RETURNING *`,
        [
          id,
          updated.title,
          updated.authors,
          updated.venue,
          updated.year,
          updated.doi,
          updated.url,
          updated.abstract,
          updated.citationCount,
          updated.referenceCount,
          updated.publicationDate,
          updated.journal,
          updated.volume,
          updated.issue,
          updated.pages,
          updated.tags,
          updated.starred,
          updated.updatedAt,
        ]
      )
      
      if (result.rows.length === 0) return null
      
      const row = result.rows[0]
      return {
        id: row.id,
        title: row.title,
        authors: row.authors,
        venue: row.venue,
        year: row.year,
        doi: row.doi,
        url: row.url,
        abstract: row.abstract,
        citationCount: row.citation_count,
        referenceCount: row.reference_count,
        publicationDate: row.publication_date,
        journal: row.journal,
        volume: row.volume,
        issue: row.issue,
        pages: row.pages,
        tags: row.tags,
        starred: row.starred,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }
    } catch (error) {
      console.error('Error updating paper:', error)
      return null
    }
  },

  delete: async (id: string): Promise<boolean> => {
    try {
      const result = await query('DELETE FROM papers WHERE id = $1', [id])
      return result.rowCount > 0
    } catch (error) {
      console.error('Error deleting paper:', error)
      return false
    }
  },
}

// Notes CRUD operations
export const notes = {
  getAll: async (): Promise<Note[]> => {
    try {
      const result = await query('SELECT * FROM notes')
      return result.rows.map(row => ({
        id: row.id,
        paperId: row.paper_id,
        bullets: row.bullets,
        whyItMatters: row.why_it_matters,
        figureRefs: row.figure_refs,
      }))
    } catch (error) {
      console.error('Error getting all notes:', error)
      return []
    }
  },

  getByPaperId: async (paperId: string): Promise<Note | undefined> => {
    try {
      const result = await query('SELECT * FROM notes WHERE paper_id = $1', [paperId])
      if (result.rows.length === 0) return undefined
      
      const row = result.rows[0]
      return {
        id: row.id,
        paperId: row.paper_id,
        bullets: row.bullets,
        whyItMatters: row.why_it_matters,
        figureRefs: row.figure_refs,
      }
    } catch (error) {
      console.error('Error getting note by paper id:', error)
      return undefined
    }
  },

  create: async (note: Note): Promise<Note> => {
    try {
      const result = await query(
        `INSERT INTO notes (id, paper_id, bullets, why_it_matters, figure_refs)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [note.id, note.paperId, note.bullets, note.whyItMatters, note.figureRefs]
      )
      
      const row = result.rows[0]
      return {
        id: row.id,
        paperId: row.paper_id,
        bullets: row.bullets,
        whyItMatters: row.why_it_matters,
        figureRefs: row.figure_refs,
      }
    } catch (error) {
      console.error('Error creating note:', error)
      throw error
    }
  },

  update: async (id: string, updates: Partial<Note>): Promise<Note | null> => {
    try {
      const existing = await notes.getByPaperId(id) // Note: using paperId as the lookup
      if (!existing) return null

      const updated = { ...existing, ...updates }
      
      const result = await query(
        `UPDATE notes 
         SET bullets = $2, why_it_matters = $3, figure_refs = $4
         WHERE paper_id = $1
         RETURNING *`,
        [id, updated.bullets, updated.whyItMatters, updated.figureRefs]
      )
      
      if (result.rows.length === 0) return null
      
      const row = result.rows[0]
      return {
        id: row.id,
        paperId: row.paper_id,
        bullets: row.bullets,
        whyItMatters: row.why_it_matters,
        figureRefs: row.figure_refs,
      }
    } catch (error) {
      console.error('Error updating note:', error)
      return null
    }
  },

  delete: async (paperId: string): Promise<boolean> => {
    try {
      const result = await query('DELETE FROM notes WHERE paper_id = $1', [paperId])
      return result.rowCount > 0
    } catch (error) {
      console.error('Error deleting note:', error)
      return false
    }
  },
}

// Collections CRUD operations
export const collections = {
  getAll: async (): Promise<Collection[]> => {
    try {
      const result = await query('SELECT * FROM collections ORDER BY name')
      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        paperIds: row.paper_ids,
        userId: row.user_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }))
    } catch (error) {
      console.error('Error getting all collections:', error)
      return []
    }
  },

  getByUserId: async (userId: string): Promise<Collection[]> => {
    try {
      const result = await query('SELECT * FROM collections WHERE user_id = $1 ORDER BY created_at DESC', [userId])
      return result.rows.map(row => ({
        id: row.id,
        name: row.name,
        paperIds: row.paper_ids,
        userId: row.user_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }))
    } catch (error) {
      console.error('Error getting collections by user id:', error)
      return []
    }
  },

  getById: async (id: string): Promise<Collection | undefined> => {
    try {
      const result = await query('SELECT * FROM collections WHERE id = $1', [id])
      if (result.rows.length === 0) return undefined

      const row = result.rows[0]
      return {
        id: row.id,
        name: row.name,
        paperIds: row.paper_ids,
        userId: row.user_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }
    } catch (error) {
      console.error('Error getting collection by id:', error)
      return undefined
    }
  },

  create: async (collection: Collection): Promise<Collection> => {
    try {
      const result = await query(
        `INSERT INTO collections (id, name, paper_ids, user_id, created_at, updated_at)
         VALUES ($1, $2, $3, $4, $5, $6)
         RETURNING *`,
        [collection.id, collection.name, collection.paperIds, collection.userId, collection.createdAt, collection.updatedAt]
      )

      const row = result.rows[0]
      return {
        id: row.id,
        name: row.name,
        paperIds: row.paper_ids,
        userId: row.user_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }
    } catch (error) {
      console.error('Error creating collection:', error)
      throw error
    }
  },

  update: async (id: string, updates: Partial<Collection>): Promise<Collection | null> => {
    try {
      const existing = await collections.getById(id)
      if (!existing) return null

      const updated = { ...existing, ...updates }

      const result = await query(
        `UPDATE collections
         SET name = $2, paper_ids = $3, updated_at = $4
         WHERE id = $1
         RETURNING *`,
        [id, updated.name, updated.paperIds, updated.updatedAt || new Date().toISOString()]
      )

      if (result.rows.length === 0) return null

      const row = result.rows[0]
      return {
        id: row.id,
        name: row.name,
        paperIds: row.paper_ids,
        userId: row.user_id,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
      }
    } catch (error) {
      console.error('Error updating collection:', error)
      return null
    }
  },

  delete: async (id: string): Promise<boolean> => {
    try {
      const result = await query('DELETE FROM collections WHERE id = $1', [id])
      return result.rowCount > 0
    } catch (error) {
      console.error('Error deleting collection:', error)
      return false
    }
  },
}

// Reviews CRUD operations
export const reviews = {
  getAll: async (): Promise<Review[]> => {
    try {
      const result = await query('SELECT * FROM reviews')
      return result.rows.map(row => ({
        paperId: row.paper_id,
        ease: row.ease,
        nextDue: row.next_due,
        lastInterval: row.last_interval || 1,
      }))
    } catch (error) {
      console.error('Error getting all reviews:', error)
      return []
    }
  },

  getByPaperId: async (paperId: string): Promise<Review | undefined> => {
    try {
      const result = await query('SELECT * FROM reviews WHERE paper_id = $1', [paperId])
      if (result.rows.length === 0) return undefined

      const row = result.rows[0]
      return {
        paperId: row.paper_id,
        ease: row.ease,
        nextDue: row.next_due,
        lastInterval: row.last_interval || 1,
      }
    } catch (error) {
      console.error('Error getting review by paper id:', error)
      return undefined
    }
  },

  create: async (review: Review): Promise<Review> => {
    try {
      // Try with lastInterval first, fall back to without it
      let result
      try {
        result = await query(
          `INSERT INTO reviews (paper_id, ease, next_due, last_interval)
           VALUES ($1, $2, $3, $4)
           RETURNING *`,
          [review.paperId, review.ease, review.nextDue, review.lastInterval || 1]
        )
      } catch (error) {
        if (error.message && error.message.includes('column "last_interval"')) {
          // Column doesn't exist, create without it
          result = await query(
            `INSERT INTO reviews (paper_id, ease, next_due)
             VALUES ($1, $2, $3)
             RETURNING *`,
            [review.paperId, review.ease, review.nextDue]
          )
        } else {
          throw error
        }
      }

      const row = result.rows[0]
      return {
        paperId: row.paper_id,
        ease: row.ease,
        nextDue: row.next_due,
        lastInterval: row.last_interval || 1,
      }
    } catch (error) {
      console.error('Error creating review:', error)
      throw error
    }
  },

  update: async (paperId: string, updates: Partial<Review>): Promise<Review | null> => {
    try {
      const existing = await reviews.getByPaperId(paperId)
      if (!existing) return null

      const updated = { ...existing, ...updates }

      // Try with lastInterval first, fall back to without it
      let result
      try {
        result = await query(
          `UPDATE reviews
           SET ease = $2, next_due = $3, last_interval = $4
           WHERE paper_id = $1
           RETURNING *`,
          [paperId, updated.ease, updated.nextDue, updated.lastInterval || 1]
        )
      } catch (error) {
        if (error.message && error.message.includes('column "last_interval"')) {
          // Column doesn't exist, update without it
          result = await query(
            `UPDATE reviews
             SET ease = $2, next_due = $3
             WHERE paper_id = $1
             RETURNING *`,
            [paperId, updated.ease, updated.nextDue]
          )
        } else {
          throw error
        }
      }

      if (result.rows.length === 0) return null

      const row = result.rows[0]
      return {
        paperId: row.paper_id,
        ease: row.ease,
        nextDue: row.next_due,
        lastInterval: row.last_interval || 1,
      }
    } catch (error) {
      console.error('Error updating review:', error)
      return null
    }
  },

  delete: async (paperId: string): Promise<boolean> => {
    try {
      const result = await query('DELETE FROM reviews WHERE paper_id = $1', [paperId])
      return result.rowCount > 0
    } catch (error) {
      console.error('Error deleting review:', error)
      return false
    }
  },
}

// Users CRUD operations
export const users = {
  getAll: async (): Promise<User[]> => {
    try {
      const result = await query('SELECT * FROM users ORDER BY created_at DESC')
      return result.rows.map(row => ({
        id: row.id,
        email: row.email,
        displayName: row.display_name,
        role: row.role,
        emailVerified: row.email_verified,
        isActive: row.is_active,
        privacySettings: row.privacy_settings,
        preferences: row.preferences,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        lastLogin: row.last_login,
      }))
    } catch (error) {
      console.error('Error getting all users:', error)
      return []
    }
  },

  getById: async (id: string): Promise<User | undefined> => {
    try {
      const result = await query('SELECT * FROM users WHERE id = $1', [id])
      if (result.rows.length === 0) return undefined

      const row = result.rows[0]
      return {
        id: row.id,
        email: row.email,
        displayName: row.display_name,
        role: row.role,
        emailVerified: row.email_verified,
        isActive: row.is_active,
        privacySettings: row.privacy_settings,
        preferences: row.preferences,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        lastLogin: row.last_login,
      }
    } catch (error) {
      console.error('Error getting user by id:', error)
      return undefined
    }
  },

  getByEmail: async (email: string): Promise<(User & { passwordHash: string }) | undefined> => {
    try {
      const result = await query('SELECT * FROM users WHERE email = $1', [email])
      if (result.rows.length === 0) return undefined

      const row = result.rows[0]
      return {
        id: row.id,
        email: row.email,
        passwordHash: row.password_hash,
        displayName: row.display_name,
        role: row.role,
        emailVerified: row.email_verified,
        isActive: row.is_active,
        privacySettings: row.privacy_settings,
        preferences: row.preferences,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        lastLogin: row.last_login,
      }
    } catch (error) {
      console.error('Error getting user by email:', error)
      return undefined
    }
  },

  create: async (user: Omit<User, 'id' | 'createdAt' | 'updatedAt'> & { passwordHash: string }): Promise<User> => {
    try {
      const result = await query(
        `INSERT INTO users (email, password_hash, display_name, role, email_verified, is_active, privacy_settings, preferences)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
         RETURNING *`,
        [
          user.email,
          user.passwordHash,
          user.displayName,
          user.role,
          user.emailVerified,
          user.isActive,
          user.privacySettings,
          user.preferences,
        ]
      )

      const row = result.rows[0]
      return {
        id: row.id,
        email: row.email,
        displayName: row.display_name,
        role: row.role,
        emailVerified: row.email_verified,
        isActive: row.is_active,
        privacySettings: row.privacy_settings,
        preferences: row.preferences,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        lastLogin: row.last_login,
      }
    } catch (error) {
      console.error('Error creating user:', error)
      throw error
    }
  },

  update: async (id: string, updates: Partial<User & { passwordHash?: string }>): Promise<User | null> => {
    try {
      const existing = await users.getById(id)
      if (!existing) return null

      const updated = { ...existing, ...updates }

      const result = await query(
        `UPDATE users
         SET email = $2, password_hash = COALESCE($3, password_hash), display_name = $4, role = $5,
             email_verified = $6, is_active = $7, privacy_settings = $8, preferences = $9, last_login = $10
         WHERE id = $1
         RETURNING *`,
        [
          id,
          updated.email,
          updates.passwordHash,
          updated.displayName,
          updated.role,
          updated.emailVerified,
          updated.isActive,
          updated.privacySettings,
          updated.preferences,
          updates.lastLogin
        ]
      )

      if (result.rows.length === 0) return null

      const row = result.rows[0]
      return {
        id: row.id,
        email: row.email,
        displayName: row.display_name,
        role: row.role,
        emailVerified: row.email_verified,
        isActive: row.is_active,
        privacySettings: row.privacy_settings,
        preferences: row.preferences,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        lastLogin: row.last_login,
      }
    } catch (error) {
      console.error('Error updating user:', error)
      return null
    }
  },

  delete: async (id: string): Promise<boolean> => {
    try {
      const result = await query('DELETE FROM users WHERE id = $1', [id])
      return result.rowCount > 0
    } catch (error) {
      console.error('Error deleting user:', error)
      return false
    }
  },
}

// User Sessions CRUD operations
export const userSessions = {
  create: async (session: Omit<UserSession, 'id' | 'createdAt'>): Promise<UserSession> => {
    try {
      const result = await query(
        `INSERT INTO user_sessions (user_id, token_hash, expires_at, ip_address, user_agent)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [session.userId, session.tokenHash, session.expiresAt, session.ipAddress, session.userAgent]
      )

      const row = result.rows[0]
      return {
        id: row.id,
        userId: row.user_id,
        tokenHash: row.token_hash,
        expiresAt: row.expires_at,
        createdAt: row.created_at,
        ipAddress: row.ip_address,
        userAgent: row.user_agent,
      }
    } catch (error) {
      console.error('Error creating user session:', error)
      throw error
    }
  },

  getByTokenHash: async (tokenHash: string): Promise<UserSession | undefined> => {
    try {
      const result = await query('SELECT * FROM user_sessions WHERE token_hash = $1', [tokenHash])
      if (result.rows.length === 0) return undefined

      const row = result.rows[0]
      return {
        id: row.id,
        userId: row.user_id,
        tokenHash: row.token_hash,
        expiresAt: row.expires_at,
        createdAt: row.created_at,
        ipAddress: row.ip_address,
        userAgent: row.user_agent,
      }
    } catch (error) {
      console.error('Error getting session by token hash:', error)
      return undefined
    }
  },

  getByUserId: async (userId: string): Promise<UserSession[]> => {
    try {
      const result = await query('SELECT * FROM user_sessions WHERE user_id = $1 ORDER BY created_at DESC', [userId])
      return result.rows.map(row => ({
        id: row.id,
        userId: row.user_id,
        tokenHash: row.token_hash,
        expiresAt: row.expires_at,
        createdAt: row.created_at,
        ipAddress: row.ip_address,
        userAgent: row.user_agent,
      }))
    } catch (error) {
      console.error('Error getting sessions by user id:', error)
      return []
    }
  },

  deleteByTokenHash: async (tokenHash: string): Promise<boolean> => {
    try {
      const result = await query('DELETE FROM user_sessions WHERE token_hash = $1', [tokenHash])
      return result.rowCount > 0
    } catch (error) {
      console.error('Error deleting session by token hash:', error)
      return false
    }
  },

  deleteByUserId: async (userId: string): Promise<boolean> => {
    try {
      const result = await query('DELETE FROM user_sessions WHERE user_id = $1', [userId])
      return result.rowCount > 0
    } catch (error) {
      console.error('Error deleting sessions by user id:', error)
      return false
    }
  },

  deleteExpired: async (): Promise<number> => {
    try {
      const result = await query('DELETE FROM user_sessions WHERE expires_at < NOW()')
      return result.rowCount || 0
    } catch (error) {
      console.error('Error deleting expired sessions:', error)
      return 0
    }
  },
}

// Password Reset Tokens CRUD operations
export const passwordResetTokens = {
  create: async (token: Omit<PasswordResetToken, 'id' | 'createdAt'>): Promise<PasswordResetToken> => {
    try {
      const result = await query(
        `INSERT INTO password_reset_tokens (user_id, token_hash, expires_at, used)
         VALUES ($1, $2, $3, $4)
         RETURNING *`,
        [token.userId, token.tokenHash, token.expiresAt, token.used]
      )

      const row = result.rows[0]
      return {
        id: row.id,
        userId: row.user_id,
        tokenHash: row.token_hash,
        expiresAt: row.expires_at,
        used: row.used,
        createdAt: row.created_at,
      }
    } catch (error) {
      console.error('Error creating password reset token:', error)
      throw error
    }
  },

  getByTokenHash: async (tokenHash: string): Promise<PasswordResetToken | undefined> => {
    try {
      const result = await query('SELECT * FROM password_reset_tokens WHERE token_hash = $1 AND used = FALSE', [tokenHash])
      if (result.rows.length === 0) return undefined

      const row = result.rows[0]
      return {
        id: row.id,
        userId: row.user_id,
        tokenHash: row.token_hash,
        expiresAt: row.expires_at,
        used: row.used,
        createdAt: row.created_at,
      }
    } catch (error) {
      console.error('Error getting password reset token by hash:', error)
      return undefined
    }
  },

  markAsUsed: async (tokenHash: string): Promise<boolean> => {
    try {
      const result = await query('UPDATE password_reset_tokens SET used = TRUE WHERE token_hash = $1', [tokenHash])
      return result.rowCount > 0
    } catch (error) {
      console.error('Error marking password reset token as used:', error)
      return false
    }
  },

  deleteExpired: async (): Promise<number> => {
    try {
      const result = await query('DELETE FROM password_reset_tokens WHERE expires_at < NOW()')
      return result.rowCount || 0
    } catch (error) {
      console.error('Error deleting expired password reset tokens:', error)
      return 0
    }
  },
}

// Email Verification Tokens CRUD operations
export const emailVerificationTokens = {
  create: async (token: Omit<EmailVerificationToken, 'id' | 'createdAt'>): Promise<EmailVerificationToken> => {
    try {
      const result = await query(
        `INSERT INTO email_verification_tokens (user_id, token_hash, expires_at, used)
         VALUES ($1, $2, $3, $4)
         RETURNING *`,
        [token.userId, token.tokenHash, token.expiresAt, token.used || false]
      )

      const row = result.rows[0]
      return {
        id: row.id,
        userId: row.user_id,
        tokenHash: row.token_hash,
        expiresAt: row.expires_at,
        used: row.used,
        createdAt: row.created_at,
      }
    } catch (error) {
      console.error('Error creating email verification token:', error)
      throw error
    }
  },

  getByTokenHash: async (tokenHash: string): Promise<EmailVerificationToken | undefined> => {
    try {
      const result = await query('SELECT * FROM email_verification_tokens WHERE token_hash = $1 AND used = FALSE', [tokenHash])
      if (result.rows.length === 0) return undefined

      const row = result.rows[0]
      return {
        id: row.id,
        userId: row.user_id,
        tokenHash: row.token_hash,
        expiresAt: row.expires_at,
        used: row.used,
        createdAt: row.created_at,
      }
    } catch (error) {
      console.error('Error getting email verification token by hash:', error)
      return undefined
    }
  },

  markAsUsed: async (tokenHash: string): Promise<boolean> => {
    try {
      const result = await query('UPDATE email_verification_tokens SET used = TRUE WHERE token_hash = $1', [tokenHash])
      return result.rowCount > 0
    } catch (error) {
      console.error('Error marking email verification token as used:', error)
      return false
    }
  },

  invalidateUserTokens: async (userId: string): Promise<number> => {
    try {
      const result = await query('UPDATE email_verification_tokens SET used = TRUE WHERE user_id = $1 AND used = FALSE', [userId])
      return result.rowCount || 0
    } catch (error) {
      console.error('Error invalidating user email verification tokens:', error)
      return 0
    }
  },

  deleteExpired: async (): Promise<number> => {
    try {
      const result = await query('DELETE FROM email_verification_tokens WHERE expires_at < NOW()')
      return result.rowCount || 0
    } catch (error) {
      console.error('Error deleting expired email verification tokens:', error)
      return 0
    }
  },
}

// Audit Logs CRUD operations
export const auditLogs = {
  create: async (log: Omit<AuditLog, 'id' | 'createdAt'>): Promise<AuditLog> => {
    try {
      const result = await query(
        `INSERT INTO audit_logs (user_id, action, resource_type, resource_id, ip_address, user_agent, details)
         VALUES ($1, $2, $3, $4, $5, $6, $7)
         RETURNING *`,
        [log.userId, log.action, log.resourceType, log.resourceId, log.ipAddress, log.userAgent, log.details]
      )

      const row = result.rows[0]
      return {
        id: row.id,
        userId: row.user_id,
        action: row.action,
        resourceType: row.resource_type,
        resourceId: row.resource_id,
        ipAddress: row.ip_address,
        userAgent: row.user_agent,
        details: row.details,
        createdAt: row.created_at,
      }
    } catch (error) {
      console.error('Error creating audit log:', error)
      throw error
    }
  },

  getByUserId: async (userId: string, limit: number = 100): Promise<AuditLog[]> => {
    try {
      const result = await query(
        'SELECT * FROM audit_logs WHERE user_id = $1 ORDER BY created_at DESC LIMIT $2',
        [userId, limit]
      )
      return result.rows.map(row => ({
        id: row.id,
        userId: row.user_id,
        action: row.action,
        resourceType: row.resource_type,
        resourceId: row.resource_id,
        ipAddress: row.ip_address,
        userAgent: row.user_agent,
        details: row.details,
        createdAt: row.created_at,
      }))
    } catch (error) {
      console.error('Error getting audit logs by user id:', error)
      return []
    }
  },

  getAll: async (limit: number = 1000): Promise<AuditLog[]> => {
    try {
      const result = await query('SELECT * FROM audit_logs ORDER BY created_at DESC LIMIT $1', [limit])
      return result.rows.map(row => ({
        id: row.id,
        userId: row.user_id,
        action: row.action,
        resourceType: row.resource_type,
        resourceId: row.resource_id,
        ipAddress: row.ip_address,
        userAgent: row.user_agent,
        details: row.details,
        createdAt: row.created_at,
      }))
    } catch (error) {
      console.error('Error getting all audit logs:', error)
      return []
    }
  },

  deleteOld: async (daysOld: number = 90): Promise<number> => {
    try {
      const result = await query(
        'DELETE FROM audit_logs WHERE created_at < NOW() - INTERVAL \'$1 days\'',
        [daysOld]
      )
      return result.rowCount || 0
    } catch (error) {
      console.error('Error deleting old audit logs:', error)
      return 0
    }
  },
}
