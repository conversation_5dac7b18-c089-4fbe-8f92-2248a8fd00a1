export interface Paper {
  id: string
  title: string
  authors: string[]
  venue?: string
  year?: number
  doi?: string
  url?: string
  abstract?: string
  citationCount?: number
  referenceCount?: number
  publicationDate?: string
  journal?: string
  volume?: string
  issue?: string
  pages?: string
  tags: string[]
  starred: boolean
  userId: string
  createdAt: string
  updatedAt: string
}

export interface Note {
  id: string
  paperId: string
  bullets: string[]
  whyItMatters?: string
  figureRefs?: string[]
  createdAt?: string
  updatedAt?: string
}

export interface Collection {
  id: string
  name: string
  paperIds: string[]
  userId: string
  createdAt?: string
  updatedAt?: string
}

export interface Review {
  paperId: string
  ease: number
  nextDue: string
  lastInterval?: number
}

// User Management Types
export interface User {
  id: string
  email: string
  displayName?: string
  role: 'admin' | 'user' | 'readonly'
  emailVerified: boolean
  isActive: boolean
  privacySettings: Record<string, any>
  preferences: Record<string, any>
  createdAt: string
  updatedAt: string
  lastLogin?: string
}

export interface UserSession {
  id: string
  userId: string
  tokenHash: string
  expiresAt: string
  createdAt: string
  ipAddress?: string
  userAgent?: string
}

export interface PasswordResetToken {
  id: string
  userId: string
  tokenHash: string
  expiresAt: string
  used: boolean
  createdAt: string
}

export interface EmailVerificationToken {
  id: string
  userId: string
  tokenHash: string
  expiresAt: string
  used: boolean
  createdAt: string
}

export interface AuditLog {
  id: string
  userId?: string
  action: string
  resourceType?: string
  resourceId?: string
  ipAddress?: string
  userAgent?: string
  details: Record<string, any>
  createdAt: string
}

// Authentication Types
export interface LoginRequest {
  email: string
  password: string
}

export interface RegisterRequest {
  email: string
  password: string
  displayName?: string
}

export interface AuthResponse {
  user: User
  token: string
  expiresAt: string
}

export interface PasswordResetRequest {
  email: string
}

export interface PasswordResetConfirm {
  token: string
  newPassword: string
}

export interface ChangePasswordRequest {
  currentPassword: string
  newPassword: string
}

export interface UpdateProfileRequest {
  displayName?: string
  privacySettings?: Record<string, any>
  preferences?: Record<string, any>
}

export interface ResendVerificationRequest {
  email: string
}

export interface VerifyEmailRequest {
  token: string
}
