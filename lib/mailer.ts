/**
 * Email service using <PERSON><PERSON><PERSON><PERSON>
 * Handles sending verification emails and other transactional emails
 */

import nodemailer from 'nodemailer'
import { config } from './config'

interface EmailOptions {
  to: string
  subject: string
  html: string
  text: string
}

class MailerError extends Error {
  constructor(message: string, public originalError?: Error) {
    super(message)
    this.name = 'MailerError'
  }
}

// Create transporter instance
let transporter: nodemailer.Transporter | null = null

function createTransporter(): nodemailer.Transporter {
  if (transporter) {
    return transporter
  }

  const transportConfig: nodemailer.TransportOptions = {
    host: config.email.smtpHost,
    port: config.email.smtpPort,
    secure: config.email.smtpTls, // true for 465, false for other ports
    auth: config.email.smtpUser && config.email.smtpPass ? {
      user: config.email.smtpUser,
      pass: config.email.smtpPass,
    } : undefined,
    // For development with Mailpit/Mailhog
    tls: {
      rejectUnauthorized: config.nodeEnv === 'production'
    }
  }

  transporter = nodemailer.createTransporter(transportConfig)
  
  // Verify connection configuration in development
  if (config.nodeEnv === 'development') {
    transporter.verify((error, success) => {
      if (error) {
        console.warn('⚠️  SMTP connection verification failed:', error.message)
        console.warn('   Email sending may not work properly')
      } else {
        console.log('✅ SMTP server is ready to take our messages')
      }
    })
  }

  return transporter
}

async function sendEmail(options: EmailOptions): Promise<void> {
  try {
    const transport = createTransporter()
    
    const mailOptions = {
      from: config.email.emailFrom,
      to: options.to,
      subject: options.subject,
      html: options.html,
      text: options.text,
    }

    const info = await transport.sendMail(mailOptions)
    
    if (config.nodeEnv === 'development') {
      console.log('📧 Email sent:', {
        messageId: info.messageId,
        to: options.to,
        subject: options.subject,
      })
    }
  } catch (error) {
    console.error('❌ Failed to send email:', error)
    throw new MailerError(
      'Failed to send email',
      error instanceof Error ? error : new Error(String(error))
    )
  }
}

function generateVerificationEmailTemplate(verificationUrl: string, userEmail: string): { html: string; text: string } {
  const html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Verify Your Email - PaperNugget</title>
      <style>
        body { font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { text-align: center; margin-bottom: 30px; }
        .logo { font-size: 24px; font-weight: bold; color: #2563eb; }
        .content { background: #f8fafc; padding: 30px; border-radius: 8px; margin: 20px 0; }
        .button { display: inline-block; background: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; font-weight: 500; }
        .button:hover { background: #1d4ed8; }
        .footer { text-align: center; margin-top: 30px; font-size: 14px; color: #6b7280; }
        .warning { background: #fef3c7; border: 1px solid #f59e0b; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <div class="logo">📄 PaperNugget</div>
        </div>
        
        <div class="content">
          <h2>Verify Your Email Address</h2>
          <p>Hello!</p>
          <p>Thank you for signing up for PaperNugget. To complete your registration and start organizing your research papers, please verify your email address by clicking the button below:</p>
          
          <p style="text-align: center; margin: 30px 0;">
            <a href="${verificationUrl}" class="button">Verify Email Address</a>
          </p>
          
          <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
          <p style="word-break: break-all; background: #f1f5f9; padding: 10px; border-radius: 4px; font-family: monospace;">
            ${verificationUrl}
          </p>
        </div>
        
        <div class="warning">
          <strong>⏰ Important:</strong> This verification link will expire in 24 hours for security reasons.
        </div>
        
        <div class="footer">
          <p>If you didn't create an account with PaperNugget, you can safely ignore this email.</p>
          <p>Need help? Contact our support team.</p>
          <p>&copy; 2024 PaperNugget. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `

  const text = `
PaperNugget - Verify Your Email Address

Hello!

Thank you for signing up for PaperNugget. To complete your registration and start organizing your research papers, please verify your email address by visiting this link:

${verificationUrl}

IMPORTANT: This verification link will expire in 24 hours for security reasons.

If you didn't create an account with PaperNugget, you can safely ignore this email.

Need help? Contact our support team.

© 2024 PaperNugget. All rights reserved.
  `

  return { html, text }
}

export async function sendVerificationEmail(to: string, token: string): Promise<void> {
  const verificationUrl = `${config.email.appUrl}/api/auth/verify-email?token=${encodeURIComponent(token)}`
  const { html, text } = generateVerificationEmailTemplate(verificationUrl, to)
  
  await sendEmail({
    to,
    subject: 'Verify your email address - PaperNugget',
    html,
    text,
  })
}

// Health check function for SMTP connectivity
export async function checkSMTPHealth(): Promise<{ healthy: boolean; error?: string }> {
  try {
    const transport = createTransporter()
    await transport.verify()
    return { healthy: true }
  } catch (error) {
    return { 
      healthy: false, 
      error: error instanceof Error ? error.message : 'Unknown SMTP error'
    }
  }
}

export { MailerError }
