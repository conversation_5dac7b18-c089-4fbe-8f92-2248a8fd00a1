import { query } from './db'

export async function runMigrations() {
  try {
    console.log('🔄 Running database migrations...')

    // Check if we need to run any migrations
    const needsMigration = await checkIfMigrationNeeded()

    if (!needsMigration) {
      console.log('✅ Database schema is up to date')
      return
    }

    console.log('📊 Database needs migration - running schema updates...')

    // Run the unified migration
    await runUnifiedMigration()

    console.log('✅ Database migrations completed successfully')
  } catch (error) {
    console.error('❌ Error running database migrations:', error)
    // Don't throw error to prevent app from crashing
  }
}

async function checkIfMigrationNeeded(): Promise<boolean> {
  try {
    // Check if the users table exists with the correct structure
    const usersTableCheck = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_name = 'users'
      );
    `)

    if (!usersTableCheck.rows[0].exists) {
      return true
    }

    // Check if papers table has user_id column
    const papersUserIdCheck = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'papers' AND column_name = 'user_id'
      );
    `)

    return !papersUserIdCheck.rows[0].exists
  } catch (error) {
    console.log('⚠️  Could not check migration status, assuming migration needed')
    return true
  }
}

async function runUnifiedMigration() {
  try {
    console.log('🔧 Running unified database migration...')

    // Enable UUID extension
    await query('CREATE EXTENSION IF NOT EXISTS "uuid-ossp";')

    // Create or update users table
    await createUsersTable()

    // Create authentication tables
    await createAuthTables()

    // Update papers table with user associations
    await updatePapersTable()

    // Update other tables
    await updateNotesTable()
    await updateCollectionsTable()
    await updateReviewsTable()

    // Create indexes
    await createIndexes()

    // Create triggers
    await createTriggers()

    // Create default users
    await createDefaultUsers()

    console.log('✅ Unified migration completed successfully')
  } catch (error) {
    console.error('❌ Error in unified migration:', error)
    throw error
  }
}

async function createUsersTable() {
  try {
    await query(`
      CREATE TABLE IF NOT EXISTS users (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        email VARCHAR(255) UNIQUE NOT NULL,
        password_hash VARCHAR(255) NOT NULL,
        display_name VARCHAR(100),
        role VARCHAR(50) DEFAULT 'user' CHECK (role IN ('admin', 'user', 'readonly')),
        email_verified BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        last_login TIMESTAMP WITH TIME ZONE,
        is_active BOOLEAN DEFAULT TRUE,
        privacy_settings JSONB DEFAULT '{}',
        preferences JSONB DEFAULT '{}'
      );
    `)
    console.log('✅ Users table created/updated')
  } catch (error) {
    console.error('❌ Error creating users table:', error)
    throw error
  }
}

async function createAuthTables() {
  try {
    // User sessions table
    await query(`
      CREATE TABLE IF NOT EXISTS user_sessions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        token_hash VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        ip_address INET,
        user_agent TEXT
      );
    `)

    // Password reset tokens table
    await query(`
      CREATE TABLE IF NOT EXISTS password_reset_tokens (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        token_hash VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `)

    // Email verification tokens table
    await query(`
      CREATE TABLE IF NOT EXISTS email_verification_tokens (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        token_hash VARCHAR(255) NOT NULL,
        expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
        used BOOLEAN DEFAULT FALSE,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `)

    // Password history table
    await query(`
      CREATE TABLE IF NOT EXISTS password_history (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
        password_hash VARCHAR(255) NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `)

    // Audit logs table
    await query(`
      CREATE TABLE IF NOT EXISTS audit_logs (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        user_id UUID REFERENCES users(id) ON DELETE SET NULL,
        action VARCHAR(100) NOT NULL,
        resource_type VARCHAR(50),
        resource_id VARCHAR(255),
        ip_address INET,
        user_agent TEXT,
        details JSONB DEFAULT '{}',
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
    `)

    console.log('✅ Authentication tables created/updated')
  } catch (error) {
    console.error('❌ Error creating auth tables:', error)
    throw error
  }
}

async function updatePapersTable() {
  try {
    // Add user_id column if it doesn't exist
    const userIdExists = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'papers' AND column_name = 'user_id'
      );
    `)

    if (!userIdExists.rows[0].exists) {
      await query(`
        ALTER TABLE papers ADD COLUMN user_id UUID REFERENCES users(id) ON DELETE CASCADE;
      `)

      // Migrate existing papers to system user
      await query(`
        UPDATE papers SET user_id = '00000000-0000-0000-0000-000000000000' WHERE user_id IS NULL;
      `)

      // Make user_id NOT NULL after migration
      await query(`
        ALTER TABLE papers ALTER COLUMN user_id SET NOT NULL;
      `)
    }

    // Add metadata columns if they don't exist
    const metadataColumns = [
      'abstract TEXT',
      'citation_count INTEGER',
      'reference_count INTEGER',
      'publication_date DATE',
      'journal VARCHAR(500)',
      'volume VARCHAR(50)',
      'issue VARCHAR(50)',
      'pages VARCHAR(50)'
    ]

    for (const columnDef of metadataColumns) {
      const columnName = columnDef.split(' ')[0]
      const columnExists = await query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns
          WHERE table_name = 'papers' AND column_name = $1
        );
      `, [columnName])

      if (!columnExists.rows[0].exists) {
        await query(`ALTER TABLE papers ADD COLUMN ${columnDef}`)
      }
    }

    console.log('✅ Papers table updated')
  } catch (error) {
    console.error('❌ Error updating papers table:', error)
    throw error
  }
}
async function updateNotesTable() {
  try {
    // Add timestamp columns if they don't exist
    const timestampColumns = ['created_at', 'updated_at']

    for (const column of timestampColumns) {
      const columnExists = await query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns
          WHERE table_name = 'notes' AND column_name = $1
        );
      `, [column])

      if (!columnExists.rows[0].exists) {
        await query(`
          ALTER TABLE notes ADD COLUMN ${column} TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        `)
      }
    }

    console.log('✅ Notes table updated')
  } catch (error) {
    console.error('❌ Error updating notes table:', error)
    throw error
  }
}

async function updateCollectionsTable() {
  try {
    // Add user_id column if it doesn't exist
    const userIdExists = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'collections' AND column_name = 'user_id'
      );
    `)

    if (!userIdExists.rows[0].exists) {
      await query(`
        ALTER TABLE collections ADD COLUMN user_id UUID REFERENCES users(id) ON DELETE CASCADE;
      `)

      // Migrate existing collections to system user
      await query(`
        UPDATE collections SET user_id = '00000000-0000-0000-0000-000000000000' WHERE user_id IS NULL;
      `)

      // Make user_id NOT NULL after migration
      await query(`
        ALTER TABLE collections ALTER COLUMN user_id SET NOT NULL;
      `)
    }

    // Add timestamp columns if they don't exist
    const timestampColumns = ['created_at', 'updated_at']

    for (const column of timestampColumns) {
      const columnExists = await query(`
        SELECT EXISTS (
          SELECT FROM information_schema.columns
          WHERE table_name = 'collections' AND column_name = $1
        );
      `, [column])

      if (!columnExists.rows[0].exists) {
        await query(`
          ALTER TABLE collections ADD COLUMN ${column} TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        `)
      }
    }

    console.log('✅ Collections table updated')
  } catch (error) {
    console.error('❌ Error updating collections table:', error)
    throw error
  }
}
async function updateReviewsTable() {
  try {
    // Add last_interval column if it doesn't exist
    const lastIntervalExists = await query(`
      SELECT EXISTS (
        SELECT FROM information_schema.columns
        WHERE table_name = 'reviews' AND column_name = 'last_interval'
      );
    `)

    if (!lastIntervalExists.rows[0].exists) {
      await query(`
        ALTER TABLE reviews ADD COLUMN last_interval INTEGER DEFAULT 1;
      `)

      // Update existing reviews to have a default last_interval of 1
      await query(`
        UPDATE reviews SET last_interval = 1 WHERE last_interval IS NULL;
      `)
    }

    console.log('✅ Reviews table updated')
  } catch (error) {
    console.error('❌ Error updating reviews table:', error)
    throw error
  }
}

async function createIndexes() {
  try {
    const indexes = [
      // User table indexes
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
      'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
      'CREATE INDEX IF NOT EXISTS idx_users_email_verified ON users(email_verified)',
      'CREATE INDEX IF NOT EXISTS idx_users_is_active ON users(is_active)',
      'CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at)',

      // Session management indexes
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_token_hash ON user_sessions(token_hash)',
      'CREATE INDEX IF NOT EXISTS idx_user_sessions_expires_at ON user_sessions(expires_at)',

      // Token management indexes
      'CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_user_id ON password_reset_tokens(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_token_hash ON password_reset_tokens(token_hash)',
      'CREATE INDEX IF NOT EXISTS idx_password_reset_tokens_expires_at ON password_reset_tokens(expires_at)',
      'CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_user_id ON email_verification_tokens(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_token_hash ON email_verification_tokens(token_hash)',
      'CREATE INDEX IF NOT EXISTS idx_email_verification_tokens_expires_at ON email_verification_tokens(expires_at)',

      // Audit log indexes
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action)',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_resource_type ON audit_logs(resource_type)',
      'CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at)',

      // Paper table indexes
      'CREATE INDEX IF NOT EXISTS idx_papers_user_id ON papers(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_papers_starred ON papers(starred)',
      'CREATE INDEX IF NOT EXISTS idx_papers_year ON papers(year)',
      'CREATE INDEX IF NOT EXISTS idx_papers_created_at ON papers(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_papers_tags ON papers USING GIN(tags)',
      'CREATE INDEX IF NOT EXISTS idx_papers_doi ON papers(doi)',
      'CREATE INDEX IF NOT EXISTS idx_papers_venue ON papers(venue)',

      // Notes table indexes
      'CREATE INDEX IF NOT EXISTS idx_notes_paper_id ON notes(paper_id)',
      'CREATE INDEX IF NOT EXISTS idx_notes_created_at ON notes(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_notes_updated_at ON notes(updated_at)',

      // Collections table indexes
      'CREATE INDEX IF NOT EXISTS idx_collections_user_id ON collections(user_id)',
      'CREATE INDEX IF NOT EXISTS idx_collections_name ON collections(name)',
      'CREATE INDEX IF NOT EXISTS idx_collections_created_at ON collections(created_at)',
      'CREATE INDEX IF NOT EXISTS idx_collections_updated_at ON collections(updated_at)',

      // Reviews table indexes
      'CREATE INDEX IF NOT EXISTS idx_reviews_next_due ON reviews(next_due)',
      'CREATE INDEX IF NOT EXISTS idx_reviews_ease ON reviews(ease)'
    ]

    for (const indexSql of indexes) {
      await query(indexSql)
    }

    console.log('✅ Database indexes created')
  } catch (error) {
    console.error('❌ Error creating indexes:', error)
    throw error
  }
}
async function createTriggers() {
  try {
    // Create the update function if it doesn't exist
    await query(`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = NOW();
          RETURN NEW;
      END;
      $$ language 'plpgsql';
    `)

    // Create triggers for automatic timestamp updates
    const triggers = [
      'DROP TRIGGER IF EXISTS update_users_updated_at ON users',
      'CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'DROP TRIGGER IF EXISTS update_papers_updated_at ON papers',
      'CREATE TRIGGER update_papers_updated_at BEFORE UPDATE ON papers FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'DROP TRIGGER IF EXISTS update_notes_updated_at ON notes',
      'CREATE TRIGGER update_notes_updated_at BEFORE UPDATE ON notes FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()',
      'DROP TRIGGER IF EXISTS update_collections_updated_at ON collections',
      'CREATE TRIGGER update_collections_updated_at BEFORE UPDATE ON collections FOR EACH ROW EXECUTE FUNCTION update_updated_at_column()'
    ]

    for (const triggerSql of triggers) {
      await query(triggerSql)
    }

    console.log('✅ Database triggers created')
  } catch (error) {
    console.error('❌ Error creating triggers:', error)
    throw error
  }
}

async function createDefaultUsers() {
  try {
    // Create default admin user
    await query(`
      INSERT INTO users (id, email, password_hash, display_name, role, email_verified, is_active)
      VALUES (
          uuid_generate_v4(),
          '<EMAIL>',
          '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G',
          'System Administrator',
          'admin',
          TRUE,
          TRUE
      ) ON CONFLICT (email) DO NOTHING;
    `)

    // Create default system user for existing data migration
    await query(`
      INSERT INTO users (id, email, password_hash, display_name, role, email_verified, is_active)
      VALUES (
          '00000000-0000-0000-0000-000000000000',
          '<EMAIL>',
          '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj/RK.s5uO.G',
          'System User',
          'admin',
          TRUE,
          TRUE
      ) ON CONFLICT (email) DO NOTHING;
    `)

    console.log('✅ Default users created')
  } catch (error) {
    console.error('❌ Error creating default users:', error)
    throw error
  }
}
// Export runMigrations for manual execution
export { runMigrations }
