"use client"

import { useState, useEffect } from "react"
import { BookOpen } from "lucide-react"
import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useToast } from "@/hooks/use-toast"
import type { Collection } from "@/lib/types"

interface CollectionToggleProps {
  paperId: string
}

export function CollectionToggle({ paperId }: CollectionToggleProps) {
  const [collections, setCollections] = useState<Collection[]>([])
  const [paperCollections, setPaperCollections] = useState<string[]>([])
  const { toast } = useToast()

  useEffect(() => {
    fetchCollections()
    fetchPaperCollections()
  }, [paperId])

  const fetchCollections = async () => {
    try {
      const response = await fetch("/api/collections")
      if (response.ok) {
        const data = await response.json()
        setCollections(data)
      }
    } catch (error) {
      console.error("Failed to fetch collections:", error)
    }
  }

  const fetchPaperCollections = async () => {
    try {
      const response = await fetch(`/api/papers/${paperId}/collections`)
      if (response.ok) {
        const data = await response.json()
        setPaperCollections(data.map((c: Collection) => c.id))
      }
    } catch (error) {
      console.error("Failed to fetch paper collections:", error)
    }
  }

  const toggleCollection = async (collectionId: string) => {
    const isInCollection = paperCollections.includes(collectionId)

    try {
      const method = isInCollection ? "DELETE" : "POST"
      await fetch(`/api/collections/${collectionId}/papers/${paperId}`, {
        method,
      })

      if (isInCollection) {
        setPaperCollections((prev) => prev.filter((id) => id !== collectionId))
        toast({ title: "Removed from collection" })
      } else {
        setPaperCollections((prev) => [...prev, collectionId])
        toast({ title: "Added to collection" })
      }
    } catch (error) {
      toast({ title: "Failed to update collection", variant: "destructive" })
    }
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm">
          <BookOpen className="h-4 w-4 mr-2" />
          Collections
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        {collections.map((collection) => (
          <DropdownMenuItem key={collection.id} onClick={() => toggleCollection(collection.id)}>
            <div className="flex items-center gap-2">
              {paperCollections.includes(collection.id) ? (
                <div className="w-4 h-4 bg-primary rounded-sm flex items-center justify-center">
                  <div className="w-2 h-2 bg-primary-foreground rounded-sm" />
                </div>
              ) : (
                <div className="w-4 h-4 border border-muted-foreground rounded-sm" />
              )}
              {collection.name}
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
