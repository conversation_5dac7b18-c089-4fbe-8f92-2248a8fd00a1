'use client'

import React, { useState } from 'react'
import { Dialog, DialogContent } from '@/components/ui/dialog'
import { LoginForm } from './LoginForm'
import { RegisterForm } from './RegisterForm'
import { ForgotPasswordForm } from './ForgotPasswordForm'

interface AuthModalProps {
  isOpen: boolean
  onClose: () => void
  defaultMode?: 'login' | 'register' | 'forgot-password'
}

export function AuthModal({ isOpen, onClose, defaultMode = 'login' }: AuthModalProps) {
  const [mode, setMode] = useState<'login' | 'register' | 'forgot-password'>(defaultMode)

  const handleSuccess = () => {
    onClose()
  }

  const switchToLogin = () => setMode('login')
  const switchToRegister = () => setMode('register')
  const switchToForgotPassword = () => setMode('forgot-password')

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md p-0 gap-0">
        {mode === 'login' ? (
          <LoginForm
            onSuccess={handleSuccess}
            onSwitchToRegister={switchToRegister}
            onForgotPassword={switchToForgotPassword}
          />
        ) : mode === 'register' ? (
          <RegisterForm
            onSuccess={handleSuccess}
            onSwitchToLogin={switchToLogin}
          />
        ) : (
          <ForgotPasswordForm
            onBack={switchToLogin}
          />
        )}
      </DialogContent>
    </Dialog>
  )
}