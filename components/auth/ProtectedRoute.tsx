'use client'

import React, { ReactNode } from 'react'
import { useAuth } from '@/lib/auth-context'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2, Lock } from 'lucide-react'

interface ProtectedRouteProps {
  children: ReactNode
  requiredRoles?: string[]
  requireEmailVerification?: boolean
  fallback?: ReactNode
  onAuthRequired?: () => void
}

export function ProtectedRoute({
  children,
  requiredRoles = [],
  requireEmailVerification = false,
  fallback,
  onAuthRequired,
}: ProtectedRouteProps) {
  const { user, isLoading, isAuthenticated } = useAuth()

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  // Not authenticated
  if (!isAuthenticated || !user) {
    if (fallback) {
      return <>{fallback}</>
    }

    return (
      <Card className="w-full max-w-md mx-auto mt-8">
        <CardHeader className="text-center">
          <Lock className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
          <CardTitle>Authentication Required</CardTitle>
          <CardDescription>
            You need to sign in to access this content.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Button
            onClick={onAuthRequired}
            className="w-full"
          >
            Sign In
          </Button>
        </CardContent>
      </Card>
    )
  }

  // Check email verification
  if (requireEmailVerification && !user.emailVerified) {
    return (
      <Card className="w-full max-w-md mx-auto mt-8">
        <CardHeader className="text-center">
          <CardTitle>Email Verification Required</CardTitle>
          <CardDescription>
            Please verify your email address to access this content.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground text-center">
            Check your email for a verification link, or contact support if you need help.
          </p>
        </CardContent>
      </Card>
    )
  }

  // Check role requirements
  if (requiredRoles.length > 0 && !requiredRoles.includes(user.role)) {
    return (
      <Card className="w-full max-w-md mx-auto mt-8">
        <CardHeader className="text-center">
          <Lock className="h-12 w-12 mx-auto text-muted-foreground mb-2" />
          <CardTitle>Access Denied</CardTitle>
          <CardDescription>
            You don't have permission to access this content.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground text-center">
            Required role: {requiredRoles.join(' or ')}
            <br />
            Your role: {user.role}
          </p>
        </CardContent>
      </Card>
    )
  }

  // All checks passed, render children
  return <>{children}</>
}