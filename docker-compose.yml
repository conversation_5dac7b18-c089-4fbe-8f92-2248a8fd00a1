version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-production}
      - DATABASE_URL=*****************************************/papernugget
      - APP_URL=${APP_URL:-http://localhost:3000}
      - EMAIL_FROM=${EMAIL_FROM:-<EMAIL>}
      - SMTP_HOST=${SMTP_HOST:-mailpit}
      - SMTP_PORT=${SMTP_PORT:-1025}
      - SMTP_USER=${SMTP_USER:-}
      - SMTP_PASS=${SMTP_PASS:-}
      - SMTP_TLS=${SMTP_TLS:-false}
    depends_on:
      db:
        condition: service_healthy
      mailpit:
        condition: service_started
    restart: unless-stopped
    networks:
      - papernugget-network

  db:
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=papernugget
      - POSTGRES_USER=papernugget
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U papernugget -d papernugget"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    networks:
      - papernugget-network

  mailpit:
    image: axllent/mailpit:latest
    ports:
      - "8025:8025"  # Web UI
      - "1025:1025"  # SMTP server
    environment:
      - MP_SMTP_AUTH_ACCEPT_ANY=1
      - MP_SMTP_AUTH_ALLOW_INSECURE=1
    restart: unless-stopped
    networks:
      - papernugget-network

volumes:
  postgres_data:
    driver: local

networks:
  papernugget-network:
    driver: bridge
