/**
 * End-to-end tests for email verification system
 * Tests the complete flow from registration to email verification
 * Run with: node --test tests/integration/email-verification-e2e.test.ts
 */

import { test, describe, before, after } from 'node:test'
import assert from 'node:assert'
import { users, emailVerificationTokens, query } from '../../lib/database'
import { checkSMTPHealth } from '../../lib/mailer'

// Test configuration
const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000'
const MAILPIT_URL = process.env.MAILPIT_URL || 'http://localhost:8025'
const TEST_EMAIL = '<EMAIL>'
const TEST_PASSWORD = 'TestPassword123!'
const TEST_DISPLAY_NAME = 'E2E Test User'

let testUserId: string
let verificationToken: string

describe('Email Verification End-to-End Tests', () => {
  before(async () => {
    // Clean up any existing test user
    try {
      const existingUser = await users.getByEmail(TEST_EMAIL)
      if (existingUser) {
        await users.delete(existingUser.id)
      }
    } catch (error) {
      // User doesn't exist, which is fine
    }

    // Verify SMTP is working
    const smtpHealth = await checkSMTPHealth()
    if (!smtpHealth.healthy) {
      console.warn('⚠️  SMTP not healthy, email tests may fail:', smtpHealth.error)
    }
  })

  after(async () => {
    // Clean up test user and related data
    if (testUserId) {
      try {
        await users.delete(testUserId)
      } catch (error) {
        console.warn('Failed to clean up test user:', error)
      }
    }
  })

  describe('User Registration with Email Verification', () => {
    test('Should register user and trigger email verification', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: TEST_PASSWORD,
          displayName: TEST_DISPLAY_NAME,
        }),
      })

      assert.strictEqual(response.status, 201, 'Registration should return 201')

      const data = await response.json()
      assert.ok(data.user, 'Response should include user data')
      assert.strictEqual(data.user.email, TEST_EMAIL, 'User email should match')
      assert.strictEqual(data.user.emailVerified, false, 'New user should not be verified')
      assert.strictEqual(data.verificationRequired, true, 'Should indicate verification required')

      testUserId = data.user.id
    })

    test('Should create verification token in database', async () => {
      // Query tokens directly since getByUserId might not exist
      const result = await query('SELECT * FROM email_verification_tokens WHERE user_id = $1 AND used_at IS NULL', [testUserId])
      assert.ok(result.rows.length > 0, 'Verification token should be created')

      const tokenRow = result.rows[0]
      assert.ok(tokenRow.token, 'Token should have a value')
      assert.ok(new Date(tokenRow.expires_at) > new Date(), 'Token should not be expired')
      assert.strictEqual(tokenRow.used_at, null, 'Token should not be used yet')

      verificationToken = tokenRow.token
    })

    test('Should send verification email', async () => {
      // Wait a moment for email to be sent
      await new Promise(resolve => setTimeout(resolve, 2000))

      try {
        // Check Mailpit for the email
        const mailpitResponse = await fetch(`${MAILPIT_URL}/api/v1/messages`)
        
        if (mailpitResponse.ok) {
          const emails = await mailpitResponse.json()
          
          // Find our verification email
          const verificationEmail = emails.messages?.find((email: any) => 
            email.To?.some((to: any) => to.Address === TEST_EMAIL) &&
            email.Subject?.includes('Verify your email')
          )

          if (verificationEmail) {
            assert.ok(verificationEmail, 'Verification email should be sent')
            assert.ok(verificationEmail.Subject.includes('Verify'), 'Email should have verification subject')
            
            // Get email content
            const contentResponse = await fetch(`${MAILPIT_URL}/api/v1/message/${verificationEmail.ID}`)
            if (contentResponse.ok) {
              const emailContent = await contentResponse.json()
              assert.ok(emailContent.HTML?.includes(verificationToken), 'Email should contain verification token')
            }
          } else {
            console.warn('⚠️  Verification email not found in Mailpit, but continuing test')
          }
        } else {
          console.warn('⚠️  Could not connect to Mailpit, skipping email verification')
        }
      } catch (error) {
        console.warn('⚠️  Email verification check failed:', error)
        // Continue with test - email sending might work even if we can't verify it
      }
    })
  })

  describe('Email Verification Process', () => {
    test('Should reject login before email verification', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: TEST_PASSWORD,
        }),
      })

      assert.strictEqual(response.status, 403, 'Unverified login should return 403')

      const data = await response.json()
      assert.strictEqual(data.code, 'EMAIL_NOT_VERIFIED', 'Should return specific error code')
    })

    test('Should verify email with valid token', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/verify-email?token=${verificationToken}`, {
        redirect: 'manual' // Don't follow redirects automatically
      })

      // Should redirect to login page
      assert.ok(response.status === 302 || response.status === 307, 'Should redirect after verification')
      
      const location = response.headers.get('location')
      assert.ok(location?.includes('/login'), 'Should redirect to login page')
      assert.ok(location?.includes('verify=ok'), 'Should include success parameter')
    })

    test('Should mark user as verified in database', async () => {
      const user = await users.getById(testUserId)
      assert.strictEqual(user?.emailVerified, true, 'User should be marked as verified')
    })

    test('Should mark token as used', async () => {
      const token = await emailVerificationTokens.getByToken(verificationToken)
      assert.ok(token?.usedAt, 'Token should be marked as used')
    })

    test('Should allow login after email verification', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: TEST_EMAIL,
          password: TEST_PASSWORD,
        }),
      })

      assert.strictEqual(response.status, 200, 'Login should succeed after verification')

      const data = await response.json()
      assert.ok(data.user, 'Response should include user data')
      assert.ok(data.token, 'Response should include auth token')
      assert.strictEqual(data.user.emailVerified, true, 'User should be verified')
    })
  })

  describe('Token Security and Edge Cases', () => {
    test('Should reject invalid verification token', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/verify-email?token=invalid-token`, {
        redirect: 'manual'
      })

      assert.ok(response.status === 302 || response.status === 307, 'Should redirect on invalid token')
      
      const location = response.headers.get('location')
      assert.ok(location?.includes('verify=invalid'), 'Should include invalid parameter')
    })

    test('Should reject already used token', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/verify-email?token=${verificationToken}`, {
        redirect: 'manual'
      })

      assert.ok(response.status === 302 || response.status === 307, 'Should redirect on used token')
      
      const location = response.headers.get('location')
      assert.ok(location?.includes('verify=invalid') || location?.includes('verify=already'), 'Should indicate token is invalid/already used')
    })

    test('Should reject missing token', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/verify-email`, {
        redirect: 'manual'
      })

      assert.ok(response.status === 302 || response.status === 307, 'Should redirect on missing token')
      
      const location = response.headers.get('location')
      assert.ok(location?.includes('verify=invalid'), 'Should include invalid parameter')
    })
  })

  describe('Email Resend Functionality', () => {
    let newTestUserId: string
    let newVerificationToken: string

    test('Should create another unverified user for resend tests', async () => {
      const newTestEmail = '<EMAIL>'
      
      // Clean up any existing user
      try {
        const existingUser = await users.getByEmail(newTestEmail)
        if (existingUser) {
          await users.delete(existingUser.id)
        }
      } catch (error) {
        // User doesn't exist, which is fine
      }

      const response = await fetch(`${BASE_URL}/api/auth/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: newTestEmail,
          password: TEST_PASSWORD,
          displayName: 'Resend Test User',
        }),
      })

      assert.strictEqual(response.status, 201, 'Registration should succeed')
      const data = await response.json()
      newTestUserId = data.user.id

      // Get the initial token
      const result = await query('SELECT * FROM email_verification_tokens WHERE user_id = $1 AND used_at IS NULL', [newTestUserId])
      newVerificationToken = result.rows[0].token
    })

    test('Should resend verification email', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/resend-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
        }),
      })

      assert.strictEqual(response.status, 200, 'Resend should return 200')

      const data = await response.json()
      assert.ok(data.message, 'Response should include message')
    })

    test('Should invalidate old token and create new one', async () => {
      // Wait a moment for the resend to process
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Old token should be marked as used
      const oldToken = await emailVerificationTokens.getByToken(newVerificationToken)
      assert.ok(oldToken?.usedAt, 'Old token should be invalidated')

      // New token should exist
      const result = await query('SELECT * FROM email_verification_tokens WHERE user_id = $1 AND used_at IS NULL', [newTestUserId])
      assert.strictEqual(result.rows.length, 1, 'Should have exactly one active token')
      assert.notStrictEqual(result.rows[0].token, newVerificationToken, 'New token should be different')
    })

    test('Should handle resend for non-existent email gracefully', async () => {
      const response = await fetch(`${BASE_URL}/api/auth/resend-verification`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
        }),
      })

      // Should return success to prevent email enumeration
      assert.strictEqual(response.status, 200, 'Should return 200 even for non-existent email')
    })

    // Clean up the resend test user
    after(async () => {
      if (newTestUserId) {
        try {
          await users.delete(newTestUserId)
        } catch (error) {
          console.warn('Failed to clean up resend test user:', error)
        }
      }
    })
  })
})
