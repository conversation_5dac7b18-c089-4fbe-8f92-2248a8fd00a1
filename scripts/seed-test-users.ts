/**
 * Seed script for creating test users for email verification testing
 * Run with: npx tsx scripts/seed-test-users.ts
 */

import { users, emailVerificationTokens } from '../lib/database'
import { hashPassword, generateSecureToken, generateTokenExpiry } from '../lib/auth'
import { sendVerificationEmail } from '../lib/mailer'

interface TestUser {
  email: string
  password: string
  displayName: string
  verified: boolean
}

const testUsers: TestUser[] = [
  {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    displayName: 'Verified User',
    verified: true,
  },
  {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    displayName: 'Unverified User',
    verified: false,
  },
  {
    email: '<EMAIL>',
    password: 'AdminPassword123!',
    displayName: 'Test Admin',
    verified: true,
  },
]

async function createTestUser(testUser: TestUser): Promise<void> {
  try {
    // Check if user already exists
    const existingUser = await users.getByEmail(testUser.email)
    if (existingUser) {
      console.log(`User ${testUser.email} already exists, skipping...`)
      return
    }

    // Hash password
    const passwordHash = await hashPassword(testUser.password)

    // Create user
    const newUser = await users.create({
      email: testUser.email,
      passwordHash,
      displayName: testUser.displayName,
      role: testUser.email.includes('admin') ? 'admin' : 'user',
      emailVerified: testUser.verified,
      isActive: true,
      privacySettings: {},
      preferences: {},
    })

    console.log(`✅ Created user: ${testUser.email} (verified: ${testUser.verified})`)

    // If user is not verified, create a verification token and send email
    if (!testUser.verified) {
      const verificationToken = generateSecureToken()
      const expiresAt = generateTokenExpiry(24) // 24 hours

      await emailVerificationTokens.create({
        userId: newUser.id,
        token: verificationToken,
        expiresAt: expiresAt.toISOString(),
        usedAt: undefined,
      })

      try {
        await sendVerificationEmail(newUser.email, verificationToken)
        console.log(`📧 Sent verification email to ${testUser.email}`)
      } catch (emailError) {
        console.warn(`⚠️  Failed to send verification email to ${testUser.email}:`, emailError)
      }
    }

  } catch (error) {
    console.error(`❌ Failed to create user ${testUser.email}:`, error)
  }
}

async function seedTestUsers(): Promise<void> {
  console.log('🌱 Seeding test users for email verification testing...')
  console.log('')

  for (const testUser of testUsers) {
    await createTestUser(testUser)
  }

  console.log('')
  console.log('✅ Test user seeding completed!')
  console.log('')
  console.log('Test Users Created:')
  console.log('==================')
  
  for (const testUser of testUsers) {
    console.log(`Email: ${testUser.email}`)
    console.log(`Password: ${testUser.password}`)
    console.log(`Status: ${testUser.verified ? 'Verified' : 'Unverified'}`)
    console.log(`Role: ${testUser.email.includes('admin') ? 'Admin' : 'User'}`)
    console.log('---')
  }

  console.log('')
  console.log('📧 Email Testing:')
  console.log('- Open http://localhost:8025 to view emails in Mailpit')
  console.log('- Try registering a new user to test the verification flow')
  console.log('- Try logging <NAME_EMAIL> to test resend functionality')
  console.log('')
}

// Run the seeding if this script is executed directly
if (require.main === module) {
  seedTestUsers()
    .then(() => {
      console.log('Seeding completed successfully')
      process.exit(0)
    })
    .catch((error) => {
      console.error('Seeding failed:', error)
      process.exit(1)
    })
}

export { seedTestUsers }
