#!/usr/bin/env npx tsx

/**
 * Email system testing script
 * Tests email functionality in different environments
 * 
 * Usage:
 *   npx tsx scripts/test-email-system.ts [--environment=dev|prod] [--send-test-email]
 */

import { checkSMTPHealth, sendVerificationEmail } from '../lib/mailer'
import { users, emailVerificationTokens } from '../lib/database'
import { generateSecureToken, generateTokenExpiry } from '../lib/auth'
import config from '../lib/config'

interface EmailTestOptions {
  environment: 'dev' | 'prod'
  sendTestEmail: boolean
  testEmail?: string
}

function parseArgs(): EmailTestOptions {
  const args = process.argv.slice(2)
  
  const options: EmailTestOptions = {
    environment: 'dev',
    sendTestEmail: false,
  }
  
  for (const arg of args) {
    if (arg.startsWith('--environment=')) {
      const env = arg.split('=')[1]
      if (env === 'dev' || env === 'prod') {
        options.environment = env
      }
    } else if (arg === '--send-test-email') {
      options.sendTestEmail = true
    } else if (arg.startsWith('--test-email=')) {
      options.testEmail = arg.split('=')[1]
    } else if (arg === '--help' || arg === '-h') {
      console.log(`
Email System Testing Script

Usage: npx tsx scripts/test-email-system.ts [options]

Options:
  --environment=dev|prod    Test environment (default: dev)
  --send-test-email         Send a test verification email
  --test-email=EMAIL        Email address for test email
  --help, -h               Show this help message

Examples:
  npx tsx scripts/test-email-system.ts
  npx tsx scripts/test-email-system.ts --environment=prod
  npx tsx scripts/test-email-system.ts --send-test-email --test-email=<EMAIL>
      `)
      process.exit(0)
    }
  }
  
  return options
}

async function testSMTPConnectivity(): Promise<boolean> {
  console.log('🔍 Testing SMTP connectivity...')
  
  try {
    const result = await checkSMTPHealth()
    
    if (result.healthy) {
      console.log('✅ SMTP connection successful')
      return true
    } else {
      console.log('❌ SMTP connection failed:', result.error)
      return false
    }
  } catch (error) {
    console.log('❌ SMTP test failed:', error)
    return false
  }
}

async function validateEmailConfiguration(): Promise<boolean> {
  console.log('🔍 Validating email configuration...')
  
  const requiredConfig = [
    { name: 'EMAIL_FROM', value: config.email.emailFrom },
    { name: 'SMTP_HOST', value: config.email.smtpHost },
    { name: 'SMTP_PORT', value: config.email.smtpPort },
    { name: 'APP_URL', value: config.email.appUrl },
  ]
  
  let allValid = true
  
  for (const configItem of requiredConfig) {
    if (!configItem.value) {
      console.log(`❌ Missing configuration: ${configItem.name}`)
      allValid = false
    } else {
      console.log(`✅ ${configItem.name}: ${configItem.name.includes('PASSWORD') ? '[REDACTED]' : configItem.value}`)
    }
  }
  
  // Check optional configuration
  const optionalConfig = [
    { name: 'SMTP_USER', value: config.email.smtpUser },
    { name: 'SMTP_PASS', value: config.email.smtpPass },
    { name: 'SMTP_TLS', value: config.email.smtpTls },
  ]
  
  for (const configItem of optionalConfig) {
    if (configItem.value) {
      console.log(`✅ ${configItem.name}: ${configItem.name.includes('PASS') ? '[REDACTED]' : configItem.value}`)
    } else {
      console.log(`⚠️  Optional ${configItem.name}: not set`)
    }
  }
  
  return allValid
}

async function testEmailTemplateGeneration(): Promise<boolean> {
  console.log('🔍 Testing email template generation...')
  
  try {
    const testToken = 'test-token-123'
    const testEmail = '<EMAIL>'
    
    // This would test the template generation without actually sending
    // We can't easily test this without refactoring the mailer module
    // For now, we'll just verify the configuration is correct
    
    const verificationUrl = `${config.email.appUrl}/api/auth/verify-email?token=${encodeURIComponent(testToken)}`
    
    if (!verificationUrl.includes('http')) {
      console.log('❌ Invalid verification URL generated:', verificationUrl)
      return false
    }
    
    console.log('✅ Email template generation test passed')
    console.log(`   Sample verification URL: ${verificationUrl}`)
    return true
  } catch (error) {
    console.log('❌ Email template generation test failed:', error)
    return false
  }
}

async function sendTestVerificationEmail(testEmail: string): Promise<boolean> {
  console.log(`📧 Sending test verification email to ${testEmail}...`)
  
  try {
    // Generate a test token
    const testToken = generateSecureToken()
    
    // Send the email
    await sendVerificationEmail(testEmail, testToken)
    
    console.log('✅ Test verification email sent successfully')
    console.log(`   Token: ${testToken}`)
    console.log(`   Verification URL: ${config.email.appUrl}/api/auth/verify-email?token=${testToken}`)
    
    return true
  } catch (error) {
    console.log('❌ Failed to send test verification email:', error)
    return false
  }
}

async function testDatabaseEmailTokens(): Promise<boolean> {
  console.log('🔍 Testing email token database operations...')
  
  try {
    // Create a test user (if not exists)
    let testUser
    try {
      testUser = await users.getByEmail('<EMAIL>')
      if (!testUser) {
        // We won't create a user here to avoid side effects
        console.log('⚠️  No test user found, skipping database token test')
        return true
      }
    } catch (error) {
      console.log('⚠️  Could not check for test user, skipping database token test')
      return true
    }
    
    // Test token creation
    const testToken = generateSecureToken()
    const expiresAt = generateTokenExpiry(24)
    
    const token = await emailVerificationTokens.create({
      userId: testUser.id,
      token: testToken,
      expiresAt: expiresAt.toISOString(),
      usedAt: undefined,
    })
    
    console.log('✅ Email token creation test passed')
    
    // Test token retrieval
    const retrievedToken = await emailVerificationTokens.getByToken(testToken)
    if (!retrievedToken) {
      console.log('❌ Failed to retrieve created token')
      return false
    }
    
    console.log('✅ Email token retrieval test passed')
    
    // Test token marking as used
    const marked = await emailVerificationTokens.markAsUsed(testToken)
    if (!marked) {
      console.log('❌ Failed to mark token as used')
      return false
    }
    
    console.log('✅ Email token marking test passed')
    
    return true
  } catch (error) {
    console.log('❌ Database email token test failed:', error)
    return false
  }
}

async function checkMailpitAvailability(): Promise<boolean> {
  console.log('🔍 Checking Mailpit availability (development)...')
  
  try {
    const mailpitUrl = process.env.MAILPIT_URL || 'http://localhost:8025'
    const response = await fetch(`${mailpitUrl}/api/v1/info`, {
      signal: AbortSignal.timeout(5000)
    })
    
    if (response.ok) {
      const info = await response.json()
      console.log('✅ Mailpit is available')
      console.log(`   Version: ${info.version || 'unknown'}`)
      console.log(`   Web UI: ${mailpitUrl}`)
      return true
    } else {
      console.log('⚠️  Mailpit responded but with error status:', response.status)
      return false
    }
  } catch (error) {
    console.log('⚠️  Mailpit not available (this is normal in production):', error instanceof Error ? error.message : 'Unknown error')
    return false
  }
}

async function main() {
  const options = parseArgs()
  
  console.log('📧 Email System Testing')
  console.log('========================')
  console.log(`Environment: ${options.environment}`)
  console.log(`Node Environment: ${process.env.NODE_ENV || 'unknown'}`)
  console.log('')
  
  const results: { test: string; passed: boolean }[] = []
  
  // Test email configuration
  const configValid = await validateEmailConfiguration()
  results.push({ test: 'Email Configuration', passed: configValid })
  
  // Test SMTP connectivity
  const smtpHealthy = await testSMTPConnectivity()
  results.push({ test: 'SMTP Connectivity', passed: smtpHealthy })
  
  // Test email template generation
  const templateValid = await testEmailTemplateGeneration()
  results.push({ test: 'Email Template Generation', passed: templateValid })
  
  // Test database operations
  const dbValid = await testDatabaseEmailTokens()
  results.push({ test: 'Database Email Tokens', passed: dbValid })
  
  // Check Mailpit (development only)
  if (options.environment === 'dev') {
    const mailpitAvailable = await checkMailpitAvailability()
    results.push({ test: 'Mailpit Availability', passed: mailpitAvailable })
  }
  
  // Send test email if requested
  if (options.sendTestEmail) {
    const testEmail = options.testEmail || '<EMAIL>'
    const emailSent = await sendTestVerificationEmail(testEmail)
    results.push({ test: 'Test Email Sending', passed: emailSent })
  }
  
  // Print summary
  console.log('\n📊 Test Results Summary')
  console.log('========================')
  
  let allPassed = true
  for (const result of results) {
    const status = result.passed ? '✅' : '❌'
    console.log(`${status} ${result.test}`)
    if (!result.passed) {
      allPassed = false
    }
  }
  
  console.log('')
  if (allPassed) {
    console.log('🎉 All email system tests passed!')
    process.exit(0)
  } else {
    console.log('💥 Some email system tests failed!')
    console.log('')
    console.log('Troubleshooting tips:')
    console.log('- Check environment variables in .env file')
    console.log('- Verify SMTP server is accessible')
    console.log('- Ensure database is running and accessible')
    console.log('- Check firewall and network connectivity')
    process.exit(1)
  }
}

// Handle unhandled errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled error during email testing:', error)
  process.exit(1)
})

process.on('SIGINT', () => {
  console.log('\n⚠️  Email testing interrupted')
  process.exit(1)
})

// Run the email system test
main().catch((error) => {
  console.error('❌ Email system test failed:', error)
  process.exit(1)
})
