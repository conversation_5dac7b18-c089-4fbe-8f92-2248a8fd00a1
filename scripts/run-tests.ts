#!/usr/bin/env npx tsx

/**
 * Comprehensive test runner script for PaperNugget
 * Runs all unit and integration tests with detailed reporting
 */

import { spawn } from 'child_process'
import { existsSync } from 'fs'
import { readdir } from 'fs/promises'
import { join } from 'path'
import { runStartupValidation } from '../lib/startup-checks'

interface TestResult {
  file: string
  passed: boolean
  output: string
  duration: number
  category: 'unit' | 'integration'
  error?: string
}

interface TestSummary {
  total: number
  passed: number
  failed: number
  duration: number
  categories: {
    unit: { total: number; passed: number; failed: number }
    integration: { total: number; passed: number; failed: number }
  }
}

async function findTestFiles(): Promise<string[]> {
  const testFiles: string[] = []

  // Find unit tests
  try {
    const unitTestDir = 'tests/unit'
    if (existsSync(unitTestDir)) {
      const unitFiles = await readdir(unitTestDir)
      for (const file of unitFiles) {
        if (file.endsWith('.test.ts')) {
          testFiles.push(join(unitTestDir, file))
        }
      }
    }
  } catch (error) {
    console.warn('Could not read unit test directory:', error)
  }

  // Find integration tests
  try {
    const integrationTestDir = 'tests/integration'
    if (existsSync(integrationTestDir)) {
      const integrationFiles = await readdir(integrationTestDir)
      for (const file of integrationFiles) {
        if (file.endsWith('.test.ts')) {
          testFiles.push(join(integrationTestDir, file))
        }
      }
    }
  } catch (error) {
    console.warn('Could not read integration test directory:', error)
  }

  return testFiles
}

function runTest(testFile: string): Promise<TestResult> {
  const start = Date.now()
  const category = testFile.includes('/unit/') ? 'unit' : 'integration'

  return new Promise((resolve) => {
    if (!existsSync(testFile)) {
      resolve({
        file: testFile,
        passed: false,
        output: '',
        duration: 0,
        category,
        error: `Test file not found: ${testFile}`,
      })
      return
    }

    console.log(`\n🧪 Running ${testFile}...`)

    const child = spawn('node', ['--test', testFile], {
      stdio: 'pipe',
      env: { ...process.env, NODE_ENV: 'test' },
    })

    let output = ''
    let errorOutput = ''

    child.stdout?.on('data', (data) => {
      output += data.toString()
    })

    child.stderr?.on('data', (data) => {
      errorOutput += data.toString()
    })

    child.on('close', (code) => {
      const duration = Date.now() - start
      const passed = code === 0

      if (passed) {
        console.log(`✅ ${testFile} passed (${duration}ms)`)
      } else {
        console.log(`❌ ${testFile} failed (${duration}ms)`)
        if (errorOutput) {
          console.log('Error output:', errorOutput)
        }
      }

      resolve({
        file: testFile,
        passed,
        output: output + errorOutput,
        duration,
        category,
        error: passed ? undefined : errorOutput || 'Test failed with non-zero exit code',
      })
    })

    child.on('error', (error) => {
      const duration = Date.now() - start
      console.log(`❌ ${testFile} failed to run: ${error.message}`)
      resolve({
        file: testFile,
        passed: false,
        output: '',
        duration,
        category,
        error: error.message,
      })
    })
  })
}

function generateSummary(results: TestResult[]): TestSummary {
  const summary: TestSummary = {
    total: results.length,
    passed: results.filter(r => r.passed).length,
    failed: results.filter(r => !r.passed).length,
    duration: results.reduce((sum, r) => sum + r.duration, 0),
    categories: {
      unit: { total: 0, passed: 0, failed: 0 },
      integration: { total: 0, passed: 0, failed: 0 }
    }
  }

  for (const result of results) {
    summary.categories[result.category].total++
    if (result.passed) {
      summary.categories[result.category].passed++
    } else {
      summary.categories[result.category].failed++
    }
  }

  return summary
}

function printSummary(summary: TestSummary, results: TestResult[]) {
  console.log('\n' + '='.repeat(60))
  console.log('📊 TEST SUMMARY')
  console.log('='.repeat(60))

  console.log(`Total Tests: ${summary.total}`)
  console.log(`✅ Passed: ${summary.passed}`)
  console.log(`❌ Failed: ${summary.failed}`)
  console.log(`⏱️  Total Duration: ${summary.duration}ms`)

  console.log('\n📂 By Category:')
  console.log(`  Unit Tests: ${summary.categories.unit.passed}/${summary.categories.unit.total} passed`)
  console.log(`  Integration Tests: ${summary.categories.integration.passed}/${summary.categories.integration.total} passed`)

  if (summary.failed > 0) {
    console.log('\n❌ Failed Tests:')
    const failedTests = results.filter(r => !r.passed)
    for (const test of failedTests) {
      console.log(`  - ${test.file}`)
      if (test.error) {
        console.log(`    Error: ${test.error.split('\n')[0]}`)
      }
    }
  }

  console.log('\n' + '='.repeat(60))

  if (summary.failed === 0) {
    console.log('🎉 All tests passed!')
  } else {
    console.log(`💥 ${summary.failed} test(s) failed`)
  }
}

async function runAllTests(): Promise<void> {
  console.log('🚀 Starting Email Verification Test Suite')
  console.log('==========================================')

  const results: TestResult[] = []

  // Run unit tests first
  console.log('\n📋 Running Unit Tests...')
  for (const testFile of testFiles.filter(f => f.includes('/unit/'))) {
    const result = await runTest(testFile)
    results.push(result)
  }

  // Run integration tests (requires running server)
  console.log('\n🔗 Running Integration Tests...')
  console.log('Note: Integration tests require the server to be running on localhost:3000')
  
  for (const testFile of testFiles.filter(f => f.includes('/integration/'))) {
    const result = await runTest(testFile)
    results.push(result)
  }

  // Summary
  console.log('\n📊 Test Results Summary')
  console.log('=======================')
  
  const passed = results.filter(r => r.passed).length
  const failed = results.filter(r => !r.passed).length
  const total = results.length

  console.log(`Total tests: ${total}`)
  console.log(`Passed: ${passed}`)
  console.log(`Failed: ${failed}`)

  if (failed > 0) {
    console.log('\n❌ Failed tests:')
    results.filter(r => !r.passed).forEach(result => {
      console.log(`  - ${result.file}: ${result.error}`)
    })
  }

  if (failed === 0) {
    console.log('\n🎉 All tests passed!')
  } else {
    console.log('\n💥 Some tests failed!')
    process.exit(1)
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  // Use the new comprehensive test runner
  async function main() {
    console.log('🧪 PaperNugget Test Suite')
    console.log('=========================')

    // Find all test files
    console.log('\n🔍 Discovering test files...')
    const testFiles = await findTestFiles()

    if (testFiles.length === 0) {
      console.log('❌ No test files found!')
      process.exit(1)
    }

    console.log(`Found ${testFiles.length} test files:`)
    testFiles.forEach(file => console.log(`  - ${file}`))

    // Run all tests
    console.log('\n🚀 Running tests...')
    const results: TestResult[] = []

    for (const testFile of testFiles) {
      const result = await runTest(testFile)
      results.push(result)
    }

    // Generate and print summary
    const summary = generateSummary(results)
    printSummary(summary, results)

    // Exit with appropriate code
    if (summary.failed > 0) {
      process.exit(1)
    } else {
      process.exit(0)
    }
  }

  main().catch((error) => {
    console.error('❌ Test runner failed:', error)
    process.exit(1)
  })
}
